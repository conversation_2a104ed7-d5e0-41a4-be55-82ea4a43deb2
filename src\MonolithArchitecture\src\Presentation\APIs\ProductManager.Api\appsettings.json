{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "App": {"Name": "ProductManager", "Version": "1.0.0", "Environment": "Development", "Database": {"DefaultConnection": "Data Source=12*******;Initial Catalog=ProductManager;Persist Security Info=True;User Id=sa;password=******;TrustServerCertificate=true", "IdentityConnection": "Data Source=12*******;Initial Catalog=ProductManager.Identity;Persist Security Info=True;User Id=sa;password=******;TrustServerCertificate=true", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "Security": {"JwtSecret": "default-secret-key-that-should-be-overridden-in-environment-specific-configs", "JwtIssuer": "ProductManager.Api", "JwtAudience": "ProductManager.Api.Users", "JwtExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "RequireHttps": false, "AllowedOrigins": ["http://localhost:3000", "http://localhost:5173"], "BearerTokenExpiration": "01:00:00", "RefreshTokenExpiration": "7.00:00:00", "EmailTokenLifetime": "24:00:00", "PhoneNumberTokenLifetime": "00:05:00", "ResetPasswordTokenLifetime": "24:00:00", "TwoFactorTokenLifetime": "00:05:00", "OtpTokenLifetime": "00:05:00", "RevokeUserSessionsDelay": "00:00:30", "Password": {"RequireDigit": false, "RequiredLength": 6, "RequireNonAlphanumeric": false, "RequireUppercase": false, "RequireLowercase": false}, "SignIn": {"RequireConfirmedAccount": false}}, "Logging": {"LogLevel": "Information", "EnableFileLogging": true, "LogPath": "logs", "MaxFileSizeMB": 10, "MaxFiles": 5, "EnableStructuredLogging": true}, "Cache": {"EnableDistributedCache": false, "ConnectionString": "", "DefaultExpirationMinutes": 30}}, "AllowedHosts": "*", "RequestSigning": {"RequireSignedRequests": false, "SecretKey": "default-request-signing-key-32-chars", "MaxTimestampAge": "00:05:00"}, "IpWhitelist": {"EnableWhitelist": false, "AllowedIps": ["12*******", "::1"]}, "RateLimit": {"EnableRateLimit": true, "MaxRequests": 100, "Window": "00:01:00"}, "Cors": {"PolicyName": "<PERSON><PERSON><PERSON>", "AllowAnyOrigin": true, "AllowAnyMethod": true, "AllowAnyHeader": true, "AllowCredentials": false}, "Swagger": {"Enabled": true, "Title": "ProductManager API", "Version": "v1.0", "EnableTryItOut": true, "DisplayRequestDuration": true}}