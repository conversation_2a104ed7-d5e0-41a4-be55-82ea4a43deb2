# ProductManager - Production-Ready Clean Architecture Monolith

[![.NET](https://img.shields.io/badge/.NET-8.0-purple.svg)](https://dotnet.microsoft.com/download/dotnet/8.0)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](#)
[![Tests](https://img.shields.io/badge/tests-137%2F137%20passing-brightgreen.svg)](#testing)
[![Production Ready](https://img.shields.io/badge/status-production%20ready-brightgreen.svg)](#status)
[![Security](https://img.shields.io/badge/security-enterprise%20grade-green.svg)](#security)
[![API Version](https://img.shields.io/badge/API-v1.0%20%7C%20v2.0-blue.svg)](#api-versioning)
[![GitHub](https://img.shields.io/badge/GitHub-hammond01/CleanArchitecture-blue.svg)](https://github.com/hammond01/CleanArchitecture)
[![Stars](https://img.shields.io/github/stars/hammond01/CleanArchitecture?style=social)](https://github.com/hammond01/CleanArchitecture/stargazers)
[![Forks](https://img.shields.io/github/forks/hammond01/CleanArchitecture?style=social)](https://github.com/hammond01/CleanArchitecture/network/members)

A **production-ready**, enterprise-grade e-commerce product management system built with **Clean Architecture** principles using **.NET 8** and **Entity Framework Core**. This monolithic application demonstrates best practices including **CQRS**, **Repository Pattern**, **Unit of Work**, **Domain-Driven Design (DDD)**, **API versioning**, **enterprise security**, and **advanced middleware pipeline**.

> **🚀 Project Status**: **Production Ready** (July 2025) - This implementation serves as the **foundation architecture** for upcoming distributed system implementations including **Microservices**, **Event-Driven**, and **Serverless** architectures. All business logic, domain models, and patterns established here will be evolved and adapted for distributed architectures.

## 🆕 Recent Updates & Production Readiness

### 🚀 Enterprise API Enhancement & Security Features (July 2025)

-   ✅ **Production-Grade Security**: Complete enterprise security implementation with JWT, CORS, IP whitelisting, rate limiting, and request signing
-   ✅ **Advanced API Documentation**: Comprehensive Swagger documentation with detailed descriptions, examples, and interactive testing
-   ✅ **Multi-Version API Support**: Full support for API versions (v1.0 and v2.0) with flexible versioning strategies
-   ✅ **Performance Optimization**: Response caching, compression (Gzip/Brotli), entity locking, and optimized middleware pipeline
-   ✅ **Enterprise Architecture Patterns**: Clean separation of concerns with pure domain models and configuration-based infrastructure
-   ✅ **Foundation for Distributed Systems**: Architecture designed to support future decomposition into microservices and event-driven patterns

### 🔒 Security & Performance Features

-   ✅ **CORS Configuration**: Environment-specific policies (Development vs Production) with flexible origin management
-   ✅ **Response Compression**: Automatic Gzip and Brotli compression for better performance
-   ✅ **Request Signing**: Optional API request integrity verification with configurable security keys
-   ✅ **IP Whitelisting**: Configurable IP-based access control for enhanced security
-   ✅ **Rate Limiting**: Configurable request throttling (default: 100 requests/minute) to prevent abuse
-   ✅ **Entity Locking**: Distributed locking system for concurrent data modification prevention
-   ✅ **Caching Strategy**: Response caching middleware for improved API performance

### 📚 API Documentation & Versioning

-   ✅ **Multi-Version Support**: Support for v1.0 and v2.0 APIs with automatic endpoint discovery
-   ✅ **Comprehensive Documentation**: Detailed API descriptions with getting started guides and feature lists
-   ✅ **Interactive Swagger UI**: Enhanced UI with custom CSS, deep linking, filtering, and validation
-   ✅ **Authentication Integration**: JWT Bearer token support with clear authentication instructions
-   ✅ **Response Examples**: Detailed response type documentation with proper HTTP status codes
-   ✅ **XML Comments**: Full XML documentation support for enhanced IntelliSense and API docs

### Business Modules Implemented (All with Enterprise-Grade Features)

-   ✅ **Product Management** - Complete product lifecycle with categories, suppliers, advanced validation, entity locking, and audit trails
-   ✅ **Category Management** - Hierarchical product categorization with full CRUD operations and business rules
-   ✅ **Supplier Management** - Vendor and supplier information management with contact tracking and performance metrics
-   ✅ **Customer Management** - Customer profiles and contact information management with comprehensive data validation
-   ✅ **Employee Management** - Staff management with roles, permissions, and organizational hierarchy
-   ✅ **Order Management** - Order processing and fulfillment with comprehensive tracking and state management
-   ✅ **Region Management** - Geographic region management for shipping and logistics with territorial boundaries
-   ✅ **Shipper Management** - Shipping provider management with complete CRUD operations and performance tracking
-   ✅ **Territory Management** - Territory and sales region management with enhanced reporting and analytics
-   ✅ **OrderDetail Management** - Order line items and details management with inventory integration
-   ✅ **Identity Management** - User authentication, authorization, JWT token management, and role-based access control

### 🎯 Architecture Foundation for Distributed Systems

This monolithic implementation serves as the **proven foundation** for upcoming distributed architectures:

-   **🏗️ Domain Model Stability**: Well-defined bounded contexts ready for service decomposition
-   **🔄 CQRS Patterns**: Command/Query separation that translates naturally to distributed systems
-   **📊 API Design**: RESTful APIs with versioning strategies suitable for service interfaces
-   **🔒 Security Model**: Enterprise-grade security patterns applicable to distributed architectures
-   **🧪 Testing Strategy**: Comprehensive testing approach that scales to distributed systems
-   **📈 Performance Patterns**: Caching, compression, and optimization strategies for distributed deployment
-   ✅ **Category Management** - Hierarchical product categorization with full CRUD operations
-   ✅ **Supplier Management** - Vendor and supplier information management
-   ✅ **Customer Management** - Customer profiles and contact information management
-   ✅ **Employee Management** - Staff management with roles and permissions
-   ✅ **Order Management** - Order processing and fulfillment with comprehensive tracking
-   ✅ **Region Management** - Geographic region management for shipping and logistics
-   ✅ **Shipper Management** - Shipping provider management with complete CRUD operations
-   ✅ **Territory Management** - Territory and sales region management (enhanced with full CRUD)
-   ✅ **OrderDetail Management** - Order line items and details management (enhanced with full CRUD)
-   ✅ **Identity Management** - User authentication, authorization, and JWT token management

### 🏗️ Infrastructure & Configuration Enhancements

-   ✅ **Entity Framework Configuration Standardization**: All entity constraints moved from annotations to IEntityTypeConfiguration classes
-   ✅ **Clean Domain Models**: Removed all configuration attributes from entities, maintaining pure domain models
-   ✅ **Consistent ID Configuration**: All ID columns configured with HasMaxLength(50) and proper constraints
-   ✅ **Migration Ready**: All configurations properly set up for database schema updates
-   ✅ **Centralized Configuration**: Infrastructure settings managed through appsettings.json with environment-specific overrides

### 🧪 Testing Infrastructure

The project includes **production-ready testing infrastructure** with complete coverage:

-   **Integration Tests**: Complete API endpoint testing with 75 test cases covering all 10 business modules
-   **Unit Tests**: Comprehensive application layer testing with 62 tests covering Product feature handlers
-   **Test Data Management**: Proper test isolation and realistic data scenarios with AutoFixture
-   **Controller Pattern Testing**: Validates standardized API response patterns (GET→DTO, POST/PUT/DELETE→Entity)
-   **Error Handling Testing**: Comprehensive validation and error scenario coverage
-   **Mock Integration**: Proper dependency mocking with Moq framework and interaction verification
-   **InternalsVisibleTo**: Configured for accessing internal members in tests

**Current Test Status:**

-   ✅ **Integration Tests**: 75/75 passing (100%) - Full CRUD coverage for all 10 business modules
-   ✅ **Unit Tests**: 62/62 passing (100%) - Comprehensive application layer validation with Product feature coverage
-   ✅ **Build Status**: All projects compile successfully
-   ✅ **API Endpoints**: All 40+ endpoints functional with proper HTTP status codes

### Unit Test Coverage Enhancements

-   ✅ **Complete Product Feature Testing**: 62 comprehensive unit tests covering all Product commands and queries
-   ✅ **Handler Testing**: Full coverage of AddOrUpdateProductHandler, DeleteProductHandler, GetProductsHandler, GetProductByIdHandler
-   ✅ **Command/Query Testing**: Comprehensive testing of all Product commands and queries with edge cases
-   ✅ **Error Handling**: Exception scenarios and null parameter handling validation
-   ✅ **Mock Integration**: Proper mocking of dependencies with verification of interactions
-   ✅ **AutoFixture Integration**: Automated test data generation with circular reference handling

### Entity Framework Configuration Improvements

-   ✅ **Standardized Configuration**: All entities now use IEntityTypeConfiguration pattern instead of annotations
-   ✅ **Clean Domain Models**: Removed all [StringLength], [Required], [Column] attributes from entities
-   ✅ **Consistent ID Configuration**: All ID columns configured with HasMaxLength(50)
-   ✅ **Proper Data Types**: Standardized column types and constraints in configuration classes
-   ✅ **Migration Ready**: All configurations properly set up for database schema updates

### Latest API Additions

-   ✅ **Territory API**: Complete CRUD operations for territory management
-   ✅ **OrderDetail API**: Complete CRUD operations for order line items
-   ✅ **Controller Standardization**: All APIs follow consistent patterns and naming conventions
-   ✅ **DTO Consistency**: Proper DTOs created for all new APIs
-   ✅ **Error Handling**: Comprehensive validation and error responses

### 🚀 OData Integration (Latest)

-   ✅ **Advanced Query Support**: OData endpoints for complex, flexible queries
-   ✅ **Category OData Controller**: Full CRUD operations with OData query capabilities
-   ✅ **Product OData Controller**: Advanced querying for product data
-   ✅ **Filtering & Sorting**: Support for `$filter`, `$orderby`, `$select`, `$expand`
-   ✅ **Pagination**: Built-in support for `$top`, `$skip`, and `$count`
-   ✅ **Metadata Support**: Full OData metadata document at `/odata/$metadata`
-   ✅ **Performance Optimized**: Configurable page sizes and query limits
-   ✅ **Documentation**: Comprehensive OData usage guide with examples

**OData Endpoints:**

-   `GET /odata/Categories` - Query categories with advanced filtering
-   `GET /odata/Products` - Query products with complex operations
-   `GET /odata/$metadata` - OData metadata document

For detailed usage examples, see [OData Integration Guide](docs/OData_Integration_Guide.md).

### Latest Commits & Improvements (December 2024)

-   🧪 **test: add comprehensive integration test suite for all controllers** - Complete test coverage with 51 integration tests
-   🔧 **fix: update Order entity to use DateTime for date properties** - Improved data consistency
-   ⚙️ **refactor: update infrastructure configuration and application setup** - Enhanced system configuration
-   🛠️ **fix: update DTOs to match Entity properties and improve data consistency** - Data model alignment
-   🚀 **feat: implement Shipper module with complete CRUD operations** - New business module
-   📊 **feat: standardize controller pattern** - Consistent API response patterns

### Business Modules Implementedrehensive Test Suite\*\*: 53 integration tests covering all controller endpoints with 100% pass rateean Architecture Monolith

[![.NET](https://img.shields.io/badge/.NET-8.0-purple.svg)](https://dotnet.- ✅ **Integration Tests**: 51/51 passing (100%) - Full CRUD covera- 🧪 **Comprehensive Test Coverage** - 53 integration tests + 2 unit tests (100% passing)e for all 7 business modules

-   ✅ **Unit Tests**: 2/2 passing (100%) - Application layer validation
-   ✅ **Build Status**: All projects compile successfully
-   ✅ **API Endpoints**: All 35+ endpoints functional with proper HTTP status codesosoft.com/download/dotnet/8.0)
    [![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
    [![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](#)
    [![Tests](https://img.shields.io/badge/tests-53%2F53%20passing-brightgreen.svg)](#testing)
    [![GitHub](https://img.shields.io/badge/GitHub-hammond01/CleanArchitecture-blue.svg)](https://github.com/hammond01/CleanArchitecture)
    [![Stars](https://img.shields.io/github/stars/hammond01/CleanArchitecture?style=social)](https://github.com/hammond01/CleanArchitecture/stargazers)
    [![Forks](https://img.shields.io/github/forks/hammond01/CleanArchitecture?style=social)](https://github.com/hammond01/CleanArchitecture/network/members)

A modern e-commerce product management system built with **Clean Architecture** principles using **.NET 8**, **Entity Framework Core**, and **Blazor**. This monolithic application demonstrates enterprise-level patterns including **CQRS**, **Repository Pattern**, **Unit of Work**, and **Domain-Driven Design (DDD)** with comprehensive test coverage and standardized API patterns.

> **Project Status**: ✅ **Production Ready** - This project implements complete Clean Architecture patterns with **standardized controller patterns**, **comprehensive test coverage (53/53 tests passing)**, and **full CRUD operations** for all business modules.

## 🆕 Recent Updates

### Controller Pattern Standardization & Complete Test Coverage (Latest)

-   ✅ **Standardized API Pattern**: All controllers now follow consistent pattern (GET returns DTO, POST/PUT/DELETE return Entity)
-   ✅ **Complete CRUD Coverage**: All 8 business modules have full Create, Read, Update, Delete operations
-   ✅ **Comprehensive Test Suite**: 77 integration tests covering all controller endpoints with 100% pass rate
-   ✅ **Shipper Module**: Complete implementation of Shipper management with CRUD operations
-   ✅ **DTO Consistency**: Fixed all DTOs to properly match Entity properties and data types
-   ✅ **Entity Model Updates**: Updated Order entity to use DateTime for consistency

### Business Modules Implemented

-   ✅ **Category Management**: Full CRUD with 6 tests
-   ✅ **Customer Management**: Full CRUD with 5 tests
-   ✅ **Employee Management**: Full CRUD with 6 tests
-   ✅ **Order Management**: Full CRUD with 5 tests
-   ✅ **Product Management**: Full CRUD with 6 tests
-   ✅ **Region Management**: Full CRUD with 2 tests (newly added)
-   ✅ **Shipper Management**: Full CRUD with 6 tests
-   ✅ **Supplier Management**: Full CRUD with 5 tests
-   ✅ **Identity Management**: Authentication & Authorization with 6 tests

### Package Management & Testing Infrastructure (Latest)

-   ✅ **Central Package Management**: Implemented centralized NuGet package management using `Directory.Packages.props`
-   ✅ **Test Project Configuration**: Fixed and updated unit and integration test projects for proper accessibility
-   ✅ **Integration Test Infrastructure**: Resolved dependency injection and routing issues for API integration tests
-   ✅ **InternalsVisibleTo Configuration**: Properly configured internal access for test assemblies
-   ✅ **Test Execution**: All unit tests passing (2/2), integration tests functional with proper API connectivity

### Code Quality Improvements

-   ✅ **Removed Global Usings**: Transitioned from `GlobalUsing.cs` files to explicit using directives across all projects
-   ✅ **Enhanced Code Clarity**: Each file now explicitly declares its dependencies for better readability
-   ✅ **Improved Maintainability**: Easier dependency tracking and better IDE IntelliSense support
-   ✅ **Added Action Logging**: Comprehensive request/response logging with `ActionLoggingFilter`
-   ✅ **Enhanced Audit Trail**: Improved audit logging with `ActionLogService` and database persistence

## 🔧 Enterprise Features

### 🛡️ Security & Authentication

```json
{
    "RequestSigning": {
        "RequireSignedRequests": false,
        "SecretKey": "your-secret-key-here",
        "MaxTimestampAge": "00:05:00"
    },
    "IpWhitelist": {
        "EnableWhitelist": false,
        "AllowedIps": ["127.0.0.1", "::1"]
    },
    "RateLimit": {
        "EnableRateLimit": true,
        "MaxRequests": 100,
        "Window": "00:01:00"
    }
}
```

**Security Features:**

-   🔐 **JWT Authentication** with refresh tokens and role-based authorization
-   🛡️ **Request Signing** for API integrity verification (optional)
-   🌐 **IP Whitelisting** for access control (configurable)
-   ⚡ **Rate Limiting** to prevent API abuse (100 requests/minute default)
-   🔒 **Entity Locking** for concurrent data modification prevention
-   📝 **Comprehensive Logging** with Serilog and database persistence

### 🚀 Performance & Optimization

-   ⚡ **Response Compression** (Gzip/Brotli) for reduced bandwidth usage
-   💾 **Response Caching** middleware for improved API performance
-   🔄 **CORS Policies** with environment-specific configurations
-   📊 **Health Checks** with detailed monitoring endpoints
-   🎯 **Entity Locking** with automatic conflict resolution
-   📈 **Performance Headers** for TTFB and caching optimization

### 📚 API Documentation & Versioning

-   📖 **Comprehensive Swagger Documentation** with examples and detailed descriptions
-   🔄 **Multi-Version API Support** (v1.0 and v2.0) with flexible version strategies
-   🎨 **Custom Swagger UI** with enhanced styling and OAuth2 integration
-   📝 **XML Documentation** for full IntelliSense support
-   🏷️ **Controller Grouping** for better API organization
-   🔍 **Deep Linking** and filtering in documentation UI

### 🎛️ Middleware Pipeline

```csharp
// Production-grade middleware pipeline
app.UseSerilogRequestLogging();           // Request logging
app.UseCompressionConfiguration();        // Response compression
app.UseCors("Development|AllowedOrigins"); // CORS policies
app.UseHealthChecks("/health");           // Health monitoring
app.UseSwaggerVersioning();               // API documentation
app.UseGlobalExceptionHandlerMiddleware(); // Global error handling
app.UseMiddleware<ApiRequestLoggingMiddleware>(); // API logging
app.UseMiddleware<AutoEntityLockMiddleware>(); // Auto-locking
app.UseAuthentication();                  // JWT authentication
app.UseAuthorization();                   // Role-based authorization
```

## 🏗️ Architecture Overview

This project follows **Clean Architecture** (Onion Architecture) principles with clear separation of concerns across four main layers:

```
┌─────────────────────────────────────┐
│           Presentation              │ ← Web API Controllers
│     (Controllers, Endpoints)        │
├─────────────────────────────────────┤
│            Application              │ ← Business Use Cases
│     (Commands, Queries, DTOs)       │
├─────────────────────────────────────┤
│              Domain                 │ ← Business Logic & Rules
│     (Entities, Repositories)        │
├─────────────────────────────────────┤
│           Infrastructure            │ ← Data Access & External Services
│   (DbContext, Repositories, etc.)   │
└─────────────────────────────────────┘
```

### 📁 Project Structure

```
src/
├── Application/                      # Application Layer
│   ├── ProductManager.Domain/        # Domain Entities & Contracts
│   └── ProductManager.Application/   # Use Cases, Commands & Queries
├── Infrastructure/                   # Infrastructure Layer
│   ├── ProductManager.Infrastructure/ # External Services
│   └── ProductManager.Persistence/   # Data Access Layer
├── Presentation/                     # Presentation Layer
│   └── APIs/
│       └── ProductManager.Api/       # RESTful Web API
├── CrossCuttingConcerns/            # Shared Components
│   ├── ProductManager.Shared/        # Common DTOs & Utilities
│   └── ProductManager.Constants/     # Application Constants
└── tests/                           # Test Projects
    ├── UnitTests/
    │   └── ProductManager.UnitTests/  # Unit Test Suite
    └── IntegrationTests/
        └── ProductManager.IntegrationTests/ # Integration Test Suite
```

**Key Configuration Files:**

-   `Directory.Packages.props` - Central NuGet package management
-   `Directory.Build.props` - Shared MSBuild properties
-   `global.json` - .NET SDK version configuration

## 🚀 Getting Started

### Prerequisites

-   [.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
-   [SQL Server](https://www.microsoft.com/en-us/sql-server) (LocalDB or SQL Server instance)
-   [Visual Studio 2022](https://visualstudio.microsoft.com/) or [VS Code](https://code.visualstudio.com/)

### Installation

1. **Clone the repository**

    ```bash
    git clone https://github.com/hammond01/CleanArchitecture.git
    cd CleanArchitecture/src/MonolithArchitecture
    ```

2. **Restore NuGet packages**

    ```bash
    dotnet restore
    ```

3. **Update database connection string and security settings**

    Update the configuration in `appsettings.json` files for your environment:

    ```json
    {
        "ConnectionStrings": {
            "SQL": "Data Source=your-server;Initial Catalog=ProductManager;Persist Security Info=True;User Id=sa;password=your-password;TrustServerCertificate=true",
            "IDENTITY": "Data Source=your-server;Initial Catalog=ProductManager.Identity;Persist Security Info=True;User Id=sa;password=your-password;TrustServerCertificate=true"
        },
        "RequestSigning": {
            "RequireSignedRequests": false,
            "SecretKey": "your-secret-key-here",
            "MaxTimestampAge": "00:05:00"
        },
        "IpWhitelist": {
            "EnableWhitelist": false,
            "AllowedIps": ["127.0.0.1", "::1"]
        },
        "RateLimit": {
            "EnableRateLimit": true,
            "MaxRequests": 100,
            "Window": "00:01:00"
        }
    }
    ```

4. **Run database migrations**

    ```bash
    dotnet ef database update --project src/Infrastructure/ProductManager.Persistence
    ```

5. **Build and run the application**

    ```bash
    # Run Web API (with enhanced features)
    dotnet run --project src/Presentation/APIs/ProductManager.Api

    # The API will be available at:
    # - HTTP: http://localhost:5151
    # - HTTPS: https://localhost:7130
    # - Swagger UI: http://localhost:5151/swagger
    # - Health Checks: http://localhost:5151/health
    ```

6. **Explore the enhanced API documentation**

    Navigate to the comprehensive Swagger documentation:

    - **Main Documentation**: `https://localhost:5001/swagger`
    - **API v1.0**: Full feature set with all business modules
    - **API v2.0**: Enhanced version with additional features
    - **Health Monitoring**: `https://localhost:5001/health/detailed` (Development only)

7. **Test the API with authentication**

    ```bash
    # Register a new user
    curl -X POST "https://localhost:5001/api/v1/identity/register" \
         -H "Content-Type: application/json" \
         -d '{"email":"<EMAIL>","password":"Test123!","confirmPassword":"Test123!"}'

    # Login to get JWT token
    curl -X POST "https://localhost:5001/api/v1/identity/login" \
         -H "Content-Type: application/json" \
         -d '{"email":"<EMAIL>","password":"Test123!"}'

    # Use the token to access protected endpoints
    curl -X GET "https://localhost:5001/api/v1/products" \
         -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
    ```

8. **Run tests**

    ```bash
    # Run all tests (53 total: 51 integration tests + 2 unit tests)
    dotnet test

    # Run unit tests only
    dotnet test tests/UnitTests/ProductManager.UnitTests.csproj

    # Run integration tests only (recommended for API validation)
    dotnet test tests/IntegrationTests/ProductManager.IntegrationTests.csproj
    ```

### 🧪 Test Coverage

This project includes **comprehensive test coverage** with standardized patterns:

```bash
# Current Test Status (All Passing ✅)
Total Tests: 137
├── Integration Tests: 75/75 passing (100%)
│   ├── CategoryController: 6 tests (full CRUD)
│   ├── CustomerController: 5 tests (full CRUD)
│   ├── EmployeeController: 6 tests (full CRUD)
│   ├── OrderController: 5 tests (full CRUD)
│   ├── ProductController: 6 tests (full CRUD)
│   ├── RegionController: 2 tests (full CRUD)
│   ├── ShipperController: 6 tests (full CRUD)
│   ├── SupplierController: 5 tests (full CRUD)
│   ├── TerritoryController: 5 tests (full CRUD) - newly added
│   ├── OrderDetailController: 5 tests (full CRUD) - newly added
│   └── IdentityController: 6 tests (auth/auth)
└── Unit Tests: 62/62 passing (100%)
    ├── AddOrUpdateProductHandler: 7 tests (business logic validation)
    ├── DeleteProductHandler: 5 tests (delete operations)
    ├── GetProductsHandler: 6 tests (query operations)
    ├── GetProductByIdHandler: 9 tests (single entity retrieval)
    ├── ProductCommandQuery: 25 tests (command/query structure validation)
    └── ProductFeatureSummary: 10 tests (comprehensive feature validation)
```

**Test Categories Covered:**

-   ✅ GET operations (Read All, Read Single)
-   ✅ POST operations (Create Valid, Create Invalid)
-   ✅ PUT operations (Update)
-   ✅ DELETE operations (Delete)
-   ✅ Error handling and validation
-   ✅ HTTP status code assertions
-   ✅ Authentication and authorization flows
-   ✅ Command/Query pattern validation
-   ✅ Handler business logic testing
-   ✅ Mock dependency verification
-   ✅ Edge cases and null parameter handling

### 📦 Package Management

This project uses **Central Package Management** with `Directory.Packages.props` for consistent dependency versioning across all projects:

```xml
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <ItemGroup>
    <!-- Core packages with centralized versions -->
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageVersion Include="AutoMapper" Version="13.0.1" />
    <!-- Test packages -->
    <PackageVersion Include="xunit" Version="2.6.2" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
  </ItemGroup>
</Project>
```

### 🧪 Testing Infrastructure

The project includes **production-ready testing infrastructure** with complete coverage:

-   **Integration Tests**: Complete API endpoint testing with 51 test cases covering all business operations
-   **Unit Tests**: Application layer testing with proper dependency injection
-   **Test Data Management**: Proper test isolation and realistic data scenarios
-   **Controller Pattern Testing**: Validates standardized API response patterns
-   **Error Handling Testing**: Comprehensive validation and error scenario coverage
-   **InternalsVisibleTo**: Configured for accessing internal members in tests

**Current Test Status:**

-   ✅ **Integration Tests**: 51/51 passing (100%) - Full CRUD coverage for all 7 business modules
-   ✅ **Unit Tests**: 2/2 passing (100%) - Application layer validation
-   ✅ **Build Status**: All projects compile successfully
-   ✅ **API Endpoints**: All 35+ endpoints functional with proper HTTP status codes

    ```

    ```

## 🗄️ Entity Framework Configuration Standards

This project demonstrates **best practices for Entity Framework Core configuration** with complete separation of concerns:

### Configuration Pattern

**Before (Entity Annotations):**

```csharp
public class Product : Entity<string>
{
    [StringLength(40)]
    [Required]
    public string ProductName { get; set; }

    [Column(TypeName = "money")]
    public decimal? UnitPrice { get; set; }
}
```

**After (Configuration Classes):**

```csharp
// Clean Domain Entity
public class Product : Entity<string>
{
    public string ProductName { get; set; }
    public decimal? UnitPrice { get; set; }
}

// Separate Configuration Class
public class ProductConfiguration : IEntityTypeConfiguration<Product>
{
    public void Configure(EntityTypeBuilder<Product> builder)
    {
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).HasMaxLength(50);
        builder.Property(x => x.ProductName).HasMaxLength(40).IsRequired();
        builder.Property(x => x.UnitPrice).HasColumnType("money");
    }
}
```

### Configuration Benefits

-   ✅ **Clean Domain Models**: No infrastructure concerns in entities
-   ✅ **Centralized Configuration**: All constraints in one place per entity
-   ✅ **Better Testability**: Domain models are pure C# classes
-   ✅ **Separation of Concerns**: Domain logic separate from persistence logic
-   ✅ **Easier Maintenance**: Configuration changes don't affect domain models
-   ✅ **Consistent Standards**: All ID columns have HasMaxLength(50)

### Configuration Files Structure

```
src/Infrastructure/ProductManager.Persistence/MappingConfigurations/
├── CategoryConfiguration.cs
├── CustomerConfiguration.cs
├── EmployeeConfiguration.cs
├── OrderConfiguration.cs
├── OrderDetailConfiguration.cs
├── ProductConfiguration.cs
├── RegionConfiguration.cs
├── ShipperConfiguration.cs
├── SupplierConfiguration.cs
└── TerritoryConfiguration.cs
```

## 🧪 Unit Testing Best Practices

This project demonstrates **comprehensive unit testing** following industry best practices:

**Test Structure & Organization:**

```
tests/UnitTests/Application/Product/
├── Handler Tests (27 tests)
│   ├── AddOrUpdateProductHandlerTests.cs    # Command handler with transaction testing
│   ├── DeleteProductHandlerTests.cs         # Delete operations with error handling
│   ├── GetProductsHandlerTests.cs           # Query operations with data validation
│   └── GetProductByIdHandlerTests.cs        # Single entity retrieval with edge cases
├── Command/Query Tests (25 tests)
│   └── ProductCommandQueryTests.cs          # CQRS pattern validation
└── Feature Integration Tests (10 tests)
    └── ProductFeatureSummaryTests.cs        # End-to-end feature validation
```

**Testing Technologies & Patterns:**

-   ✅ **xUnit** - Modern .NET testing framework
-   ✅ **FluentAssertions** - Expressive assertions with clear failure messages
-   ✅ **Moq** - Dependency mocking with interaction verification
-   ✅ **AutoFixture** - Automated test data generation with circular reference handling
-   ✅ **AAA Pattern** - Arrange, Act, Assert structure for clarity
-   ✅ **Theory/InlineData** - Parameterized tests for multiple scenarios

**Test Coverage Areas:**

```csharp
// Example: Comprehensive handler testing
[Fact]
public async Task HandleAsync_WhenProductIdIsNull_ShouldCreateNewProduct()
{
    // Arrange - Setup test data and mocks
    var product = _fixture.Build<Products>()
        .With(p => p.Id, (string)null!)
        .Create();
    var command = new AddOrUpdateProductCommand(product);

    // Act - Execute the operation
    var result = await _handler.HandleAsync(command);

    // Assert - Verify outcomes and interactions
    result.StatusCode.Should().Be(201);
    result.Message.Should().Be(CRUDMessage.CreateSuccess);
    product.Id.Should().NotBeNullOrEmpty(); // ID should be generated

    _crudServiceMock.Verify(x => x.AddAsync(It.IsAny<Products>(),
        It.IsAny<CancellationToken>()), Times.Once);
}
```

**Key Testing Features:**

-   ✅ **Transaction Testing**: Proper transaction handling with disposal verification
-   ✅ **Error Scenario Coverage**: Exception handling and edge case validation
-   ✅ **Mock Verification**: Ensuring correct service interactions
-   ✅ **Null Parameter Handling**: Comprehensive null safety testing
-   ✅ **CQRS Validation**: Command/Query pattern compliance testing
-   ✅ **Feature Integration**: End-to-end business scenario validation

## 🌐 API Endpoints & Documentation

### 📋 Complete API Coverage

The API provides **comprehensive CRUD operations** for all business modules with **enhanced documentation** and **validation**:

**📊 Product Management API** (Enhanced with validation & locking)

```http
GET    /api/v1/products              # Get all products with pagination
GET    /api/v1/products/{id}         # Get product by ID
POST   /api/v1/products              # Create new product (with validation)
PUT    /api/v1/products/{id}         # Update product (with entity locking)
DELETE /api/v1/products/{id}         # Delete product (with entity locking)
```

**📂 Category Management API**

```http
GET    /api/v1/categories            # Get all categories
GET    /api/v1/categories/{id}       # Get category by ID
POST   /api/v1/categories            # Create new category
PUT    /api/v1/categories/{id}       # Update category
DELETE /api/v1/categories/{id}       # Delete category
```

**👥 Customer & Employee Management APIs**

```http
# Customer Management
GET    /api/v1/customers             # Get all customers
GET    /api/v1/customers/{id}        # Get customer by ID
POST   /api/v1/customers             # Create new customer
PUT    /api/v1/customers/{id}        # Update customer
DELETE /api/v1/customers/{id}        # Delete customer

# Employee Management
GET    /api/v1/employees             # Get all employees
GET    /api/v1/employees/{id}        # Get employee by ID
POST   /api/v1/employees             # Create new employee
PUT    /api/v1/employees/{id}        # Update employee
DELETE /api/v1/employees/{id}        # Delete employee
```

**📦 Order & OrderDetail Management APIs**

```http
# Order Management
GET    /api/v1/orders                # Get all orders
GET    /api/v1/orders/{id}           # Get order by ID
POST   /api/v1/orders                # Create new order
PUT    /api/v1/orders/{id}           # Update order
DELETE /api/v1/orders/{id}           # Delete order

# OrderDetail Management
GET    /api/v1/orderdetails          # Get all order details
GET    /api/v1/orderdetails/{id}     # Get order detail by ID
POST   /api/v1/orderdetails          # Create new order detail
PUT    /api/v1/orderdetails/{id}     # Update order detail
DELETE /api/v1/orderdetails/{id}     # Delete order detail
```

**🏢 Business Management APIs**

```http
# Supplier Management
GET    /api/v1/suppliers             # Get all suppliers
GET    /api/v1/suppliers/{id}        # Get supplier by ID
POST   /api/v1/suppliers             # Create new supplier
PUT    /api/v1/suppliers/{id}        # Update supplier
DELETE /api/v1/suppliers/{id}        # Delete supplier

# Shipper Management
GET    /api/v1/shippers              # Get all shippers
GET    /api/v1/shippers/{id}         # Get shipper by ID
POST   /api/v1/shippers              # Create new shipper
PUT    /api/v1/shippers/{id}         # Update shipper
DELETE /api/v1/shippers/{id}         # Delete shipper
```

**🌍 Geographic Management APIs**

```http
# Region Management
GET    /api/v1/regions               # Get all regions
GET    /api/v1/regions/{id}          # Get region by ID
POST   /api/v1/regions               # Create new region
PUT    /api/v1/regions/{id}          # Update region
DELETE /api/v1/regions/{id}          # Delete region

# Territory Management
GET    /api/v1/territories           # Get all territories
GET    /api/v1/territories/{id}      # Get territory by ID
POST   /api/v1/territories           # Create new territory
PUT    /api/v1/territories/{id}      # Update territory
DELETE /api/v1/territories/{id}      # Delete territory
```

**🔐 Authentication & Identity APIs**

```http
POST   /api/v1/identity/register     # User registration
POST   /api/v1/identity/login        # User login (JWT token)
POST   /api/v1/identity/refresh      # Refresh JWT token
POST   /api/v1/identity/logout       # User logout
GET    /api/v1/identity/profile      # Get user profile
PUT    /api/v1/identity/profile      # Update user profile
```

**📊 System & Monitoring APIs**

```http
GET    /health                       # Basic health check
GET    /health/ready                 # Readiness probe
GET    /health/live                  # Liveness probe
GET    /health/detailed              # Detailed health info (Development only)
GET    /swagger                      # API documentation
```

### 🔄 API Versioning Support

```http
# Multiple versioning strategies supported:
GET /api/v1/products                  # URL segment versioning
GET /api/products?version=1.0         # Query string versioning
GET /api/products?v=1                 # Short query string versioning
GET /api/products                     # Header: X-API-Version: 1.0
```

### 📚 Enhanced Swagger Documentation

Access comprehensive API documentation at `/swagger` with:

-   🎨 **Custom UI styling** with enhanced readability
-   🔐 **JWT authentication** integration with Bearer token support
-   📖 **Detailed endpoint descriptions** with examples and use cases
-   🔄 **Multiple API versions** (v1.0 and v2.0) with automatic discovery
-   🏷️ **Controller grouping** for better organization
-   🔍 **Interactive testing** with request/response examples
-   📋 **Response type documentation** with proper HTTP status codes

## 🛠️ Technologies & Patterns

### Core Technologies

-   **.NET 8.0** - Latest .NET framework with improved performance
-   **ASP.NET Core** - High-performance web framework
-   **Entity Framework Core 8.0** - Advanced ORM with configuration standards
-   **SQL Server** - Enterprise-grade relational database

### Design Patterns & Principles

-   **Clean Architecture** - Onion architecture with clear separation of concerns
-   **CQRS** (Command Query Responsibility Segregation) - Separate read/write operations
-   **Repository Pattern** - Data access abstraction with Unit of Work
-   **Domain-Driven Design (DDD)** - Business-focused domain modeling
-   **Dependency Injection** - Built-in IoC container with scoped lifetimes
-   **API Versioning** - Multiple version strategies with backward compatibility
-   **Entity Locking** - Distributed locking for concurrent operations
-   **Middleware Pipeline** - Production-grade request processing pipeline

### Key Libraries & Frameworks

-   **Asp.Versioning** (8.1.0) - Comprehensive API versioning support
-   **MediatR** (12.4.1) - CQRS and mediator pattern implementation
-   **Mapster** (7.4.0) - High-performance object mapping
-   **Serilog** (4.2.0) - Structured logging with multiple sinks
-   **Swashbuckle.AspNetCore** (7.2.0) - OpenAPI/Swagger documentation
-   **Microsoft.AspNetCore.Authentication.JwtBearer** (8.0.10) - JWT authentication
-   **Microsoft.EntityFrameworkCore** (8.0.10) - Entity Framework Core ORM
-   **xUnit** (2.6.2) - Modern testing framework
-   **FluentAssertions** (6.12.0) - Expressive test assertions
-   **Moq** (4.20.72) - Mocking framework for unit tests
-   **AutoFixture** (4.18.1) - Test data generation

### Security & Performance Features

-   **JWT Authentication** - Secure token-based authentication with refresh tokens
-   **CORS Policies** - Environment-specific cross-origin resource sharing
-   **Response Compression** - Gzip and Brotli compression for better performance
-   **Request Signing** - Optional API integrity verification
-   **Rate Limiting** - Configurable request throttling and abuse prevention
-   **IP Whitelisting** - Access control based on client IP addresses
-   **Entity Locking** - Concurrent modification prevention with automatic conflict resolution
-   **Response Caching** - HTTP caching headers and server-side caching
-   **Health Checks** - Comprehensive application and database health monitoring

### Testing Libraries

-   **xUnit** (2.6.2) - Testing framework
-   **FluentAssertions** (6.12.0) - Fluent test assertions
-   **Microsoft.AspNetCore.Mvc.Testing** (8.0.10) - Integration testing
-   **AutoFixture** (4.18.1) - Test data generation
-   **Testcontainers** (3.9.0) - Container-based testing infrastructure

## 📋 Features

### Core Business Modules (All with Full CRUD)

-   🛍️ **Product Management** - Complete product lifecycle with categories and suppliers
-   📦 **Category Management** - Hierarchical product categorization
-   🏪 **Supplier Management** - Vendor and supplier information
-   👤 **Customer Management** - Customer profiles and contact information
-   👤 **Employee Management** - Staff management with roles and permissions
-   📝 **Order Management** - Order processing and fulfillment
-   � **OrderDetail Management** - Order line items and details management (newly added)
-   🌍 **Region Management** - Geographic region management
-   🗺️ **Territory Management** - Territory and sales region management (newly added)
-   🚚 **Shipper Management** - Shipping provider management
-   🔐 **Identity Management** - User authentication and authorization

### Technical Features

-   🏗️ **Standardized API Patterns** - Consistent controller behavior (GET→DTO, POST/PUT/DELETE→Entity)
-   🧪 **Comprehensive Test Coverage** - 53 integration tests + 2 unit tests (100% passing)
-   📦 **Central Package Management** - Consistent NuGet versioning across all projects
-   📝 **Explicit Using Directives** - Clear dependency management without global usings
-   📊 **Enhanced Audit Logging** - Comprehensive tracking with ActionLogService and database persistence
-   � **API Request Logging** - Detailed request/response logging with ApiRequestLoggingMiddleware
-   🎯 **Action Logging Filter** - Automatic logging of controller actions and performance metrics
-   📝 **Audit Trail** - Complete system change tracking via AuditLogEntry
-   📊 **API Monitoring** - Request/response logging with ApiLogItem
-   🏥 **Health Checks** - Application monitoring (planned)
-   📚 **API Documentation** - Comprehensive Swagger documentation
-   🔄 **Real-time Updates** - SignalR integration (planned)
-   🌐 **Multi-database Support** - Separate databases for main data and identity

## 🏛️ Architecture Details

### Domain Layer (`ProductManager.Domain`)

-   **Entities**: Core business entities (Product, Category, Order, etc.)
-   **Repository Interfaces**: Data access contracts
-   **Domain Events**: Business event definitions
-   **Value Objects**: Immutable domain objects
-   **Enums**: Domain-specific enumerations

```csharp
public abstract class Entity<TKey> : IHasKey<TKey>, ITrackable
{
    public TKey Id { get; set; } = default!;
    public DateTimeOffset CreatedDateTime { get; set; }
    public DateTimeOffset? UpdatedDateTime { get; set; }
}
```

### Application Layer (`ProductManager.Application`)

-   **Commands**: Write operations (Create, Update, Delete)
-   **Queries**: Read operations (Get, List, Search)
-   **Handlers**: Business logic implementation
-   **DTOs**: Data transfer objects
-   **Mappings**: Object transformation logic

```csharp
public class AddOrUpdateProductCommand : ICommand<ApiResponse>
{
    public Products Products { get; set; }
}

internal class AddOrUpdateProductHandler : ICommandHandler<AddOrUpdateProductCommand, ApiResponse>
{
    // Implementation details...
}
```

### Infrastructure Layer

#### Persistence (`ProductManager.Persistence`)

-   **DbContext**: Entity Framework configuration
-   **Repository Implementations**: Data access logic
-   **Migrations**: Database schema changes
-   **Configurations**: Entity mappings
-   **Services**:
    -   `ActionLogService`: Persist action logs to database
    -   `LockManager`: Distributed locking implementation

#### Infrastructure (`ProductManager.Infrastructure`)

-   **External Services**: Third-party integrations
-   **Middleware**: Request/response pipeline components
    -   `ActionLoggingFilter`: Automatic action logging and performance tracking
    -   `ApiRequestLoggingMiddleware`: Comprehensive request/response logging
    -   `GlobalExceptionHandlerMiddleware`: Centralized error handling
-   **Identity Services**: User authentication and authorization
-   **Logging Services**: Structured logging with Serilog
-   **DateTime Providers**: Abstracted time services for testing

### Presentation Layer

#### Web API (`ProductManager.Api`)

-   **Controllers**: HTTP endpoints
-   **Middleware**: Request/response pipeline
-   **Filters**: Cross-cutting concerns
-   **Configuration**: Startup logic

## 🔧 Code Quality & Architecture Standards

### Explicit Using Directives

This project has been migrated from global usings to explicit using directives for:

-   **Better Code Clarity**: Each file explicitly declares its dependencies
-   **Improved IDE Support**: Enhanced IntelliSense and navigation
-   **Easier Dependency Tracking**: Clear understanding of what each file needs
-   **Better Maintainability**: Easier to refactor and understand code dependencies

### Example of Explicit Using Directives:

```csharp
using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using ProductManager.Application.Common.Commands;
using ProductManager.Domain.Common;
using ProductManager.Domain.Entities;
using ProductManager.Domain.Repositories;

namespace ProductManager.Application.Feature.Product.Commands
{
    public class AddOrUpdateProductCommand : ICommand<ApiResponse>
    {
        public Products Products { get; set; }
    }
}
```

### Logging & Monitoring

-   **Action Logging**: Automatic logging of all controller actions with performance metrics
-   **Request/Response Logging**: Detailed HTTP request and response logging
-   **Audit Trail**: Comprehensive audit logging for data changes
-   **Structured Logging**: Serilog integration with structured logging patterns

## 🔧 Configuration

### Application Settings

Key configuration options in `appsettings.json`:

```json
{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
    "ConnectionStrings": {
        "SQL": "Data Source=127.0.0.1;Initial Catalog=ProductManager;Persist Security Info=True;User Id=sa;password=******;TrustServerCertificate=true",
        "IDENTITY": "Data Source=127.0.0.1;Initial Catalog=ProductManager.Identity;Persist Security Info=True;User Id=sa;password=******;TrustServerCertificate=true"
    },
    "AllowedHosts": "*"
}
    }
}
```

### Database Configuration

The application uses **Entity Framework Core** with **SQL Server**:

-   **Code-First approach** with migrations
-   **IEntityTypeConfiguration pattern** for all entity configurations
-   **Clean domain models** without configuration attributes
-   **Standardized ID constraints** with HasMaxLength(50) for all primary keys
-   **Proper column types and constraints** defined in configuration classes
-   **Automatic relationship mapping**
-   **Audit trail tracking**
-   **Soft delete patterns**

## 🧪 Testing

> **Note**: Test projects are not yet implemented in this solution. This is a planned feature for future development.

### Planned Test Structure

-   **Unit Tests** - Domain & Application logic (To be implemented)
-   **Integration Tests** - API endpoints (To be implemented)
-   **Functional Tests** - End-to-end scenarios (To be implemented)

### Future Testing Implementation

```bash
# Planned test commands (when implemented)
dotnet test
dotnet test --collect:"XPlat Code Coverage"
```

## 📦 Deployment

### Development

```bash
dotnet run --project src/Presentation/APIs/ProductManager.Api --environment Development
```

### Production

```bash
# Publish the application
dotnet publish -c Release -o ./publish

# Run published application
cd publish
dotnet ProductManager.Api.dll
```

### Docker (Optional)

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/Presentation/APIs/ProductManager.Api/ProductManager.Api.csproj", "src/Presentation/APIs/ProductManager.Api/"]
# ... other COPY commands
RUN dotnet restore
RUN dotnet build -c Release -o /app/build

FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ProductManager.Api.dll"]
```

## 📚 API Documentation

Once the application is running, you can access:

-   **Swagger UI**: `http://localhost:5151/swagger`
-   **API Endpoints**: `http://localhost:5151/api`

### Available API Endpoints

All controllers follow the **standardized pattern**: GET operations return DTOs for optimized data transfer, while POST/PUT/DELETE operations return Entities for complete object state.

**API Pattern:**

-   `GET` operations → Return **DTO** (Data Transfer Object)
-   `POST/PUT/DELETE` operations → Return **Entity** (Full domain object)

**Controller Endpoints:**

-   `GET /api/Product` - List all products (returns ProductDto)
-   `GET /api/Product/{id}` - Get product by ID (returns ProductDto)
-   `POST /api/Product` - Create new product (returns Product entity)
-   `PUT /api/Product` - Update product (returns Product entity)
-   `DELETE /api/Product/{id}` - Delete product (returns Product entity)
-   `GET /api/Category` - Category management (returns CategoryDto[])
-   `POST /api/Category` - Create category (returns Category entity)
-   `GET /api/Customer` - Customer management (returns CustomerDto[])
-   `POST /api/Customer` - Create customer (returns Customer entity)
-   `GET /api/Employee` - Employee management (returns EmployeeDto[])
-   `POST /api/Employee` - Create employee (returns Employee entity)
-   `GET /api/Order` - Order management (returns OrderDto[])
-   `POST /api/Order` - Create order (returns Order entity)
-   `GET /api/Shipper` - Shipper management (returns ShipperDto[])
-   `POST /api/Shipper` - Create shipper (returns Shipper entity)
-   `GET /api/Supplier` - Supplier management (returns SupplierDto[])
-   `POST /api/Supplier` - Create supplier (returns Supplier entity)
-   `GET /api/Territory` - Territory management (returns TerritoryDto[]) - newly added
-   `POST /api/Territory` - Create territory (returns Territory entity) - newly added
-   `GET /api/OrderDetail` - OrderDetail management (returns OrderDetailDto[]) - newly added
-   `POST /api/OrderDetail` - Create order detail (returns OrderDetail entity) - newly added
-   `POST /api/Identity` - Identity/Authentication endpoints

## 🔧 Development & Quality Assurance

### Central Package Management Implementation

This project has been upgraded to use **Central Package Management** for consistent dependency versioning:

**Benefits:**

-   ✅ Consistent package versions across all projects
-   ✅ Simplified dependency management
-   ✅ Reduced package reference conflicts
-   ✅ Easier maintenance and updates

**Implementation Details:**

```xml
<!-- Directory.Packages.props -->
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <ItemGroup>
    <!-- Example centralized versions -->
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageVersion Include="MediatR" Version="12.4.1" />
    <PackageVersion Include="Mapster" Version="7.4.0" />
  </ItemGroup>
</Project>
```

### Testing Infrastructure Enhancements

**Recent Test Infrastructure Improvements:**

-   ✅ **Fixed Test Project Configuration**: Resolved corrupted project files and updated to central package management
-   ✅ **Integration Test Setup**: Properly configured API integration tests with `WebApplicationFactory<Program>`
-   ✅ **Dependency Injection Fixes**: Resolved `IDistributedCache` registration issues for test environments
-   ✅ **InternalsVisibleTo Configuration**: Enabled proper access to internal members for comprehensive testing
-   ✅ **AutoFixture Configuration**: Fixed circular reference issues in unit tests

**Test Execution Status:**

```bash
# Current Test Results - All Passing ✅
dotnet test
# Result: 137/137 tests passing (100% success rate)
#   - Unit Tests: 62/62 passing
#   - Integration Tests: 75/75 passing

# Unit Tests - Application Logic ✅
dotnet test tests/UnitTests/
# Result: 62/62 tests passing (comprehensive Product feature validation)
#   - Handler Tests: 27 tests (all CRUD handlers)
#   - Command/Query Tests: 25 tests (structure validation)
#   - Feature Tests: 10 tests (integration scenarios)

# Integration Tests - API Endpoints ✅
dotnet test tests/IntegrationTests/
# Result: 75/75 tests passing (complete CRUD coverage for all 10 modules)
```

**Test Project Structure:**

```
tests/
├── UnitTests/
│   ├── Application/Product/
│   │   ├── AddOrUpdateProductHandlerTests.cs     # Command handler tests (7 tests)
│   │   ├── DeleteProductHandlerTests.cs          # Delete handler tests (5 tests)
│   │   ├── GetProductsHandlerTests.cs            # Query handler tests (6 tests)
│   │   ├── GetProductByIdHandlerTests.cs         # Single query handler tests (9 tests)
│   │   ├── ProductCommandQueryTests.cs           # Command/Query structure tests (25 tests)
│   │   └── ProductFeatureSummaryTests.cs         # Feature integration tests (10 tests)
│   └── ProductManager.UnitTests.csproj           # Unit test project (62 total tests)
└── IntegrationTests/
    ├── Controllers/
    │   ├── CategoryControllerTests.cs             # Category API tests (6 tests)
    │   ├── CustomerControllerTests.cs             # Customer API tests (5 tests)
    │   ├── EmployeeControllerTests.cs             # Employee API tests (6 tests)
    │   ├── IdentityControllerTests.cs             # Identity API tests (6 tests)
    │   ├── OrderControllerTests.cs                # Order API tests (5 tests)
    │   ├── ProductControllerTests.cs              # Product API tests (6 tests)
    │   ├── ShipperControllerTests.cs              # Shipper API tests (6 tests)
    │   ├── SupplierControllerTests.cs             # Supplier API tests (5 tests)
    │   ├── TerritoryControllerTests.cs            # Territory API tests (5 tests)
    │   └── OrderDetailControllerTests.cs          # OrderDetail API tests (5 tests)
    └── ProductManager.IntegrationTests.csproj     # Integration test project (75 total tests)
```

### Quality Assurance Checklist

**Build & Compilation:**

-   ✅ Solution builds without errors
-   ✅ All projects compile successfully
-   ✅ NuGet packages restore correctly

**Testing:**

-   ✅ Unit tests execute and pass
-   ✅ Integration tests can connect to API
-   ✅ Test projects properly reference application code

**Code Quality:**

-   ✅ Explicit using directives (no global usings)
-   ✅ SOLID principles applied
-   ✅ Clean Architecture layers maintained
-   ✅ Comprehensive logging implemented

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Coding Standards

-   Follow **C# coding conventions**
-   Use **meaningful names** for variables and methods
-   **Explicit using directives** - No global usings
-   Write **unit tests** for new features
-   Update **documentation** as needed
-   Follow **SOLID principles**
-   Implement **proper logging** for new features
-   Use **structured logging** patterns with Serilog

## 📝 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

## 👥 Authors

-   **Hammond** - _Initial work & Project Maintainer_ - [GitHub Profile](https://github.com/hammond01)
-   **Project Repository** - [CleanArchitecture](https://github.com/hammond01/CleanArchitecture)

### 📧 Contact

-   **GitHub**: [@hammond01](https://github.com/hammond01)
-   **Email**: <EMAIL>
-   **Project Issues**: [GitHub Issues](https://github.com/hammond01/CleanArchitecture/issues)

## 🙏 Acknowledgments

-   **Clean Architecture** by Robert C. Martin
-   **Domain-Driven Design** by Eric Evans
-   **.NET Community** for excellent documentation
-   **Microsoft** for the amazing .NET platform

## 🔗 Useful Links

### Project Resources

-   **Main Repository**: [CleanArchitecture](https://github.com/hammond01/CleanArchitecture)
-   **Issues & Bug Reports**: [GitHub Issues](https://github.com/hammond01/CleanArchitecture/issues)
-   **Feature Requests**: [GitHub Discussions](https://github.com/hammond01/CleanArchitecture/discussions)
-   **Latest Releases**: [GitHub Releases](https://github.com/hammond01/CleanArchitecture/releases)

### Documentation & Learning

-   [Clean Architecture Guide](https://docs.microsoft.com/en-us/dotnet/architecture/modern-web-apps-azure/common-web-application-architectures)
-   [.NET 8 Documentation](https://docs.microsoft.com/en-us/dotnet/)
-   [Entity Framework Core](https://docs.microsoft.com/en-us/ef/core/)
-   [ASP.NET Core Web API](https://docs.microsoft.com/en-us/aspnet/core/web-api/)
-   [MediatR Documentation](https://github.com/jbogard/MediatR)

---

**Last Updated**: July 4, 2025 | **Version**: 3.0 Production Ready | **Happy Coding! 🚀**

## 🚀 Future Roadmap & Related Projects

### 🎯 Upcoming Architecture Implementations

This monolithic implementation serves as the **foundation** for a series of architectural patterns:

#### 🔮 [Microservices Architecture](../MicroservicesArchitecture/README.md) - Q3 2025

-   **Service Decomposition**: Extract bounded contexts into independent services
-   **API Gateway**: Centralized routing with load balancing
-   **Service Mesh**: Advanced service-to-service communication
-   **Distributed Data**: Database per service pattern
-   **Container Orchestration**: Kubernetes deployment

#### 🔮 [Event-Driven Architecture](../EventDrivenArchitecture/README.md) - Q4 2025

-   **Event Sourcing**: Complete event-based state management
-   **Event Bus**: Publish-subscribe communication patterns
-   **Saga Patterns**: Distributed transaction management
-   **CQRS Enhancement**: Advanced read/write model separation
-   **Event Replay**: Historical state reconstruction

#### 🔮 [Serverless Architecture](../ServerlessArchitecture/README.md) - Q1 2026

-   **Azure Functions**: Function-as-a-Service implementation
-   **Event-Driven Triggers**: HTTP, queue, and timer-based execution
-   **Managed Services**: Cloud-native data and messaging
-   **Cost Optimization**: Pay-per-execution pricing models
-   **Auto-Scaling**: Serverless compute scaling

### 📊 Architecture Comparison Study

A comprehensive comparison study will be conducted across all implementations:

-   **Performance Benchmarks**: Throughput, latency, and resource utilization
-   **Scalability Analysis**: Horizontal and vertical scaling characteristics
-   **Operational Complexity**: Deployment, monitoring, and maintenance overhead
-   **Development Velocity**: Feature development speed and team productivity
-   **Cost Analysis**: Infrastructure, operational, and development costs
-   **Reliability Metrics**: Fault tolerance and system availability

### 🎓 Learning Resources

**Documentation Series:**

-   [Clean Architecture Fundamentals](../../docs/CleanArchitecture.md)
-   [Domain-Driven Design Patterns](../../docs/DDD.md)
-   [Microservices Decomposition Guide](../../docs/MicroservicesDecomposition.md)
-   [Event-Driven Architecture Patterns](../../docs/EventDrivenPatterns.md)
-   [Serverless Architecture Strategies](../../docs/ServerlessStrategies.md)

**Video Series:** (Planned)

-   Architecture Evolution Journey
-   Service Boundary Identification
-   Distributed System Patterns
-   Performance Optimization Strategies

### 🔄 Migration Path

**From Monolith to Microservices:**

1. **Strangler Fig Pattern**: Gradually extract services
2. **Database Decomposition**: Split shared data stores
3. **API Gateway Introduction**: Centralize routing
4. **Service Discovery**: Implement service registry
5. **Monitoring & Observability**: Distributed tracing

**From Microservices to Event-Driven:**

1. **Event Identification**: Identify domain events
2. **Event Store Implementation**: Persistent event storage
3. **Projection Building**: Create read models
4. **Event Bus Integration**: Publish-subscribe patterns
5. **Saga Implementation**: Distributed transactions

**From Event-Driven to Serverless:**

1. **Function Extraction**: Convert use cases to functions
2. **Event Triggers**: Map events to function triggers
3. **Managed Service Migration**: Move to cloud services
4. **Cost Optimization**: Right-size function resources
5. **Monitoring Integration**: Serverless observability

### 🌟 Community & Contributions

**Open Source Contribution:**

-   Each architectural pattern will be open-sourced
-   Community feedback will guide implementation decisions
-   Best practices will be documented and shared
-   Performance benchmarks will be publicly available

**Community Engagement:**

-   [GitHub Discussions](https://github.com/hammond01/CleanArchitecture/discussions)
-   [Architecture Blog Series](https://hammond01.dev/clean-architecture-series)
-   [LinkedIn Updates](https://linkedin.com/in/hammond01)
-   [YouTube Channel](https://youtube.com/@hammond01-dev) (Planned)

---

⭐ **Follow this project to stay updated on the architecture evolution journey!** ⭐

🚀 **Star the repository if you find this implementation helpful for your enterprise projects!** 🚀
