<Project>

  <PropertyGroup>
    <LangVersion>12.0</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1998;CS1591</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);CS0114</WarningsAsErrors>

    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>1591</NoWarn> <!-- Remove this to turn on warnings for missing XML Comments -->
  </PropertyGroup>
</Project>