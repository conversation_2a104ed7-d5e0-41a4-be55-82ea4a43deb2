name: CI/CD Pipeline

on:
    push:
        branches: [main, develop]
    pull_request:
        branches: [main]

env:
    DOTNET_VERSION: "8.0"
    SOLUTION_PATH: "src/MonolithArchitecture"

jobs:
    test:
        name: Test
        runs-on: ubuntu-latest

        services:
            sqlserver:
                image: mcr.microsoft.com/mssql/server:2022-latest
                env:
                    ACCEPT_EULA: Y
                    SA_PASSWORD: TestPassword123!
                ports:
                    - 1433:1433
                options: >-
                    --health-cmd="/opt/mssql-tools/bin/sqlcmd -S localhost -U SA -P 'TestPassword123!' -Q 'SELECT 1'"
                    --health-interval=10s
                    --health-timeout=5s
                    --health-retries=5

        steps:
            - uses: actions/checkout@v4

            - name: Setup .NET
              uses: actions/setup-dotnet@v4
              with:
                  dotnet-version: ${{ env.DOTNET_VERSION }}

            - name: Restore dependencies
              run: dotnet restore ${{ env.SOLUTION_PATH }}

            - name: Build
              run: dotnet build ${{ env.SOLUTION_PATH }} --configuration Release --no-restore

            - name: Run Unit Tests
              run: dotnet test ${{ env.SOLUTION_PATH }}/tests/UnitTests --configuration Release --no-build --verbosity normal --collect:"XPlat Code Coverage"

            - name: Run Integration Tests
              run: dotnet test ${{ env.SOLUTION_PATH }}/tests/IntegrationTests --configuration Release --no-build --verbosity normal
              env:
                  ConnectionStrings__SQL: "Data Source=localhost;Initial Catalog=ProductManager;User Id=sa;Password=TestPassword123!;TrustServerCertificate=true"
                  ConnectionStrings__IDENTITY: "Data Source=localhost;Initial Catalog=ProductManager.Identity;User Id=sa;Password=TestPassword123!;TrustServerCertificate=true"

            - name: Code Coverage Report
              uses: codecov/codecov-action@v3
              with:
                  files: ./**/coverage.cobertura.xml
                  fail_ci_if_error: true

    build-and-push:
        name: Build and Push Docker Image
        runs-on: ubuntu-latest
        needs: test
        if: github.ref == 'refs/heads/main'

        steps:
            - uses: actions/checkout@v4

            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v3

            - name: Login to Docker Hub
              uses: docker/login-action@v3
              with:
                  username: ${{ secrets.DOCKER_USERNAME }}
                  password: ${{ secrets.DOCKER_PASSWORD }}

            - name: Build and push Docker image
              uses: docker/build-push-action@v5
              with:
                  context: ${{ env.SOLUTION_PATH }}
                  file: ${{ env.SOLUTION_PATH }}/Dockerfile
                  push: true
                  tags: |
                      ${{ secrets.DOCKER_USERNAME }}/productmanager-api:latest
                      ${{ secrets.DOCKER_USERNAME }}/productmanager-api:${{ github.sha }}
                  cache-from: type=gha
                  cache-to: type=gha,mode=max

    security-scan:
        name: Security Scan
        runs-on: ubuntu-latest
        needs: test

        steps:
            - uses: actions/checkout@v4

            - name: Run Trivy vulnerability scanner
              uses: aquasecurity/trivy-action@master
              with:
                  scan-type: "fs"
                  scan-ref: "${{ env.SOLUTION_PATH }}"
                  format: "sarif"
                  output: "trivy-results.sarif"

            - name: Upload Trivy scan results to GitHub Security tab
              uses: github/codeql-action/upload-sarif@v3
              with:
                  sarif_file: "trivy-results.sarif"
