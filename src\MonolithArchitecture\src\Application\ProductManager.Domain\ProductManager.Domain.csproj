﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Mapster"/>
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.EntityFrameworkCore"/>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="ObjectCloner.Extensions"/>
    <PackageReference Include="Serilog"/>
    <PackageReference Include="Serilog.AspNetCore"/>
    <PackageReference Include="Serilog.Enrichers.Environment"/>
    <PackageReference Include="Serilog.Exceptions"/>
    <PackageReference Include="Ulid"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\CrossCuttingConcerns\ProductManager.Shared\ProductManager.Shared.csproj"/>
  </ItemGroup>

</Project>
