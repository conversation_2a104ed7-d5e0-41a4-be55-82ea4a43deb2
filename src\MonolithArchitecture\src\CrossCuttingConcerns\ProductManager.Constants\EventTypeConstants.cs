namespace ProductManager.Constants;

public class EventTypeConstants
{
    public const string CategoryCreated = "CATEGORY_CREATED";
    public const string CategoryUpdated = "CATEGORY_UPDATED";
    public const string CategoryDeleted = "CATEGORY_DELETED";

    public const string ProductCreated = "PRODUCT_CREATED";
    public const string ProductUpdated = "PRODUCT_UPDATED";
    public const string ProductDeleted = "PRODUCT_DELETED";

    public const string SupplierCreated = "SUPPLIER_CREATED";
    public const string SupplierUpdated = "SUPPLIER_UPDATED";
    public const string SupplierDeleted = "SUPPLIER_DELETED";

    public const string OrderCreated = "ORDER_CREATED";
    public const string OrderUpdated = "ORDER_UPDATED";
    public const string OrderDeleted = "ORDER_DELETED";

    public const string CustomerCreated = "CUSTOMER_CREATED";
    public const string CustomerUpdated = "CUSTOMER_UPDATED";
    public const string CustomerDeleted = "CUSTOMER_DELETED";

    public const string EmployeeCreated = "EMPLOYEE_CREATED";
    public const string EmployeeUpdated = "EMPLOYEE_UPDATED";
    public const string EmployeeDeleted = "EMPLOYEE_DELETED";

    public const string ShipperCreated = "SHIPPER_CREATED";
    public const string ShipperUpdated = "SHIPPER_UPDATED";
    public const string ShipperDeleted = "SHIPPER_DELETED";
}
