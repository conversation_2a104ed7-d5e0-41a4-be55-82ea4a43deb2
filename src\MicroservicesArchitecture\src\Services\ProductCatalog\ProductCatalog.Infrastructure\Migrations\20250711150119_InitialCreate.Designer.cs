﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using ProductCatalog.Infrastructure.Data;

#nullable disable

namespace ProductCatalog.Infrastructure.Migrations
{
    [DbContext(typeof(ProductCatalogDbContext))]
    [Migration("20250711150119_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("ProductCatalog.Domain.Entities.Category", b =>
                {
                    b.Property<string>("CategoryId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("CategoryName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<byte[]>("Picture")
                        .HasColumnType("image");

                    b.Property<string>("PictureLink")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("CategoryId");

                    b.HasIndex("CategoryName")
                        .HasDatabaseName("IX_Categories_CategoryName");

                    b.ToTable("Categories", (string)null);

                    b.HasData(
                        new
                        {
                            CategoryId = "01JH179GGZ7FAHZ0DNFYNZ21BB",
                            CategoryName = "Electronics",
                            CreatedAt = new DateTime(2025, 7, 11, 15, 1, 18, 1, DateTimeKind.Utc).AddTicks(2719),
                            Description = "Electronic devices and accessories",
                            PictureLink = "https://example.com/electronics.jpg"
                        },
                        new
                        {
                            CategoryId = "01JH179GGZ7FAHZ0DNFYNZ23DD",
                            CategoryName = "Mobile Phones",
                            CreatedAt = new DateTime(2025, 7, 11, 15, 1, 18, 1, DateTimeKind.Utc).AddTicks(2721),
                            Description = "Smartphones and mobile accessories",
                            PictureLink = "https://example.com/phones.jpg"
                        },
                        new
                        {
                            CategoryId = "01JH179GGZ7FAHZ0DNFYNZ24EE",
                            CategoryName = "Computers",
                            CreatedAt = new DateTime(2025, 7, 11, 15, 1, 18, 1, DateTimeKind.Utc).AddTicks(2721),
                            Description = "Laptops, desktops and computer accessories",
                            PictureLink = "https://example.com/computers.jpg"
                        });
                });

            modelBuilder.Entity("ProductCatalog.Domain.Entities.Product", b =>
                {
                    b.Property<string>("ProductId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("CategoryId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Discontinued")
                        .HasColumnType("bit");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)");

                    b.Property<string>("QuantityPerUnit")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<short?>("ReorderLevel")
                        .HasColumnType("smallint");

                    b.Property<string>("SupplierId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("UnitPrice")
                        .HasColumnType("money");

                    b.Property<short?>("UnitsInStock")
                        .HasColumnType("smallint");

                    b.Property<short?>("UnitsOnOrder")
                        .HasColumnType("smallint");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ProductId");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("IX_Products_CategoryId");

                    b.HasIndex("ProductName")
                        .HasDatabaseName("IX_Products_ProductName");

                    b.HasIndex("SupplierId")
                        .HasDatabaseName("IX_Products_SupplierId");

                    b.ToTable("Products", (string)null);

                    b.HasData(
                        new
                        {
                            ProductId = "01JH179GGZ7FAHZ0DNFYNZ20AA",
                            CategoryId = "01JH179GGZ7FAHZ0DNFYNZ21BB",
                            CreatedAt = new DateTime(2025, 7, 11, 15, 1, 18, 1, DateTimeKind.Utc).AddTicks(1507),
                            Discontinued = false,
                            ProductName = "Laptop Dell XPS 13",
                            QuantityPerUnit = "1 unit",
                            ReorderLevel = (short)10,
                            SupplierId = "01JH179GGZ7FAHZ0DNFYNZ18YX",
                            UnitPrice = 1299.99m,
                            UnitsInStock = (short)50,
                            UnitsOnOrder = (short)0
                        },
                        new
                        {
                            ProductId = "01JH179GGZ7FAHZ0DNFYNZ22CC",
                            CategoryId = "01JH179GGZ7FAHZ0DNFYNZ23DD",
                            CreatedAt = new DateTime(2025, 7, 11, 15, 1, 18, 1, DateTimeKind.Utc).AddTicks(1510),
                            Discontinued = false,
                            ProductName = "iPhone 15 Pro",
                            QuantityPerUnit = "1 unit",
                            ReorderLevel = (short)5,
                            SupplierId = "01JH179GGZ7FAHZ0DNFYNZ19FG",
                            UnitPrice = 999.99m,
                            UnitsInStock = (short)30,
                            UnitsOnOrder = (short)20
                        });
                });

            modelBuilder.Entity("ProductCatalog.Domain.Entities.Supplier", b =>
                {
                    b.Property<string>("SupplierId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Address")
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("City")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)");

                    b.Property<string>("ContactName")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("ContactTitle")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("Country")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Fax")
                        .HasMaxLength(24)
                        .HasColumnType("nvarchar(24)");

                    b.Property<string>("HomePage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasMaxLength(24)
                        .HasColumnType("nvarchar(24)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Region")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("SupplierId");

                    b.HasIndex("CompanyName")
                        .HasDatabaseName("IX_Suppliers_CompanyName");

                    b.HasIndex("PostalCode")
                        .HasDatabaseName("IX_Suppliers_PostalCode");

                    b.ToTable("Suppliers", (string)null);

                    b.HasData(
                        new
                        {
                            SupplierId = "01JH179GGZ7FAHZ0DNFYNZ18YX",
                            Address = "123 Tech Street",
                            City = "TechCity",
                            CompanyName = "Tech Supplies Co.",
                            ContactName = "John Doe",
                            ContactTitle = "Sales Manager",
                            Country = "Techland",
                            CreatedAt = new DateTime(2025, 7, 11, 15, 1, 18, 1, DateTimeKind.Utc).AddTicks(4291),
                            Fax = "************",
                            HomePage = "https://github.com/hammond01",
                            Phone = "************",
                            PostalCode = "12345",
                            Region = "TechRegion"
                        },
                        new
                        {
                            SupplierId = "01JH179GGZ7FAHZ0DNFYNZ19FG",
                            Address = "456 Mobile Blvd",
                            City = "MobileCity",
                            CompanyName = "Mobile Accessories Inc.",
                            ContactName = "Jane Smith",
                            ContactTitle = "CEO",
                            Country = "PhoneCountry",
                            CreatedAt = new DateTime(2025, 7, 11, 15, 1, 18, 1, DateTimeKind.Utc).AddTicks(4293),
                            Fax = "************",
                            HomePage = "https://github.com/hammond01",
                            Phone = "************",
                            PostalCode = "67890",
                            Region = "MobileRegion"
                        });
                });

            modelBuilder.Entity("ProductCatalog.Domain.Entities.Product", b =>
                {
                    b.HasOne("ProductCatalog.Domain.Entities.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProductCatalog.Domain.Entities.Supplier", "Supplier")
                        .WithMany("Products")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("ProductCatalog.Domain.Entities.Category", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("ProductCatalog.Domain.Entities.Supplier", b =>
                {
                    b.Navigation("Products");
                });
#pragma warning restore 612, 618
        }
    }
}
