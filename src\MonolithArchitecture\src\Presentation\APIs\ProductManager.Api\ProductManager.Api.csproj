<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>ProductManager.IntegrationTests</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi"/>
    <PackageReference Include="Asp.Versioning.Mvc"/>
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer"/>
    <PackageReference Include="Swashbuckle.AspNetCore"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design"/>
    <PackageReference Include="Microsoft.AspNetCore.OData"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Application\ProductManager.Application\ProductManager.Application.csproj"/>
    <ProjectReference Include="..\..\..\CrossCuttingConcerns\ProductManager.Shared\ProductManager.Shared.csproj"/>
  </ItemGroup>

</Project>
