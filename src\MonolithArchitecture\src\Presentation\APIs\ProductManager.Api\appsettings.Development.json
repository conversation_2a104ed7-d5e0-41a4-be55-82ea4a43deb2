{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information", "ProductManager": "Debug"}}, "App": {"Name": "ProductManager Development", "Version": "1.0.0-dev", "Environment": "Development", "Database": {"DefaultConnection": "Data Source=127.0.0.1;Initial Catalog=ProductManager_Dev;Persist Security Info=True;User Id=sa;password=******;TrustServerCertificate=true", "IdentityConnection": "Data Source=127.0.0.1;Initial Catalog=ProductManager_Identity_Dev;Persist Security Info=True;User Id=sa;password=******;TrustServerCertificate=true", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": true}, "Security": {"JwtSecret": "development-super-secret-key-that-is-at-least-32-characters-long-for-jwt-signing", "JwtIssuer": "http://localhost:5073", "JwtAudience": "ProductManager.Api.Development", "JwtExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "RequireHttps": false, "AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "https://localhost:7000"], "BearerTokenExpiration": "01:00:00", "RefreshTokenExpiration": "7.00:00:00", "EmailTokenLifetime": "24:00:00", "PhoneNumberTokenLifetime": "00:05:00", "ResetPasswordTokenLifetime": "24:00:00", "TwoFactorTokenLifetime": "00:05:00", "OtpTokenLifetime": "00:05:00", "RevokeUserSessionsDelay": "00:00:30", "Password": {"RequireDigit": false, "RequiredLength": 6, "RequireNonAlphanumeric": false, "RequireUppercase": false, "RequireLowercase": false}, "SignIn": {"RequireConfirmedAccount": false}}, "Logging": {"LogLevel": "Debug", "EnableFileLogging": true, "LogPath": "logs/development", "MaxFileSizeMB": 10, "MaxFiles": 10, "EnableStructuredLogging": true}, "Cache": {"EnableDistributedCache": false, "ConnectionString": "", "DefaultExpirationMinutes": 30}}, "RequestSigning": {"RequireSignedRequests": false, "SecretKey": "development-request-signing-key-32-chars", "MaxTimestampAge": "00:05:00"}, "IpWhitelist": {"EnableWhitelist": false, "AllowedIps": ["127.0.0.1", "::1", "localhost"]}, "RateLimit": {"EnableRateLimit": false, "MaxRequests": 1000, "Window": "00:01:00"}, "Cors": {"PolicyName": "Development", "AllowAnyOrigin": true, "AllowAnyMethod": true, "AllowAnyHeader": true, "AllowCredentials": false}, "Swagger": {"Enabled": true, "Title": "ProductManager Development API", "Version": "v1.0", "EnableTryItOut": true, "DisplayRequestDuration": true}}