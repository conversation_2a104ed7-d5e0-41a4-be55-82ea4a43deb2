<Project>
  <ItemGroup>
    <PackageVersion Include="Mapster" Version="7.4.0" />
    <PackageVersion Include="Mapster.DependencyInjection" Version="1.0.1" />
    <PackageVersion Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageVersion Include="BootstrapBlazor.Analyzers" Version="1.0.0" />
    <PackageVersion Include="BootstrapBlazor.MaterialDesign" Version="9.0.1" />
    <PackageVersion Include="Breeze.Persistence.EFCore" Version="7.3.0" />
    <PackageVersion Include="Humanizer.Core" Version="2.14.1" />
    <PackageVersion Include="MediatR" Version="12.4.1" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
    <PackageVersion Include="Microsoft.AspNetCore.Authorization" Version="8.0.10" />
    <PackageVersion Include="Microsoft.AspNetCore.DataProtection.EntityFrameworkCore" Version="8.0.10" />
    <PackageVersion Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.10" />
    <PackageVersion Include="Asp.Versioning.Mvc" Version="8.0.0" />
    <PackageVersion Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OData" Version="9.3.2" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="8.0.11" />
    <PackageVersion Include="Microsoft.Data.SqlClient" Version="5.1.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Abstractions" Version="8.0.10" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.10" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.11" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions" Version="8.0.11" />
    <PackageVersion Include="Microsoft.Extensions.Identity.Core" Version="8.0.10" />
    <PackageVersion Include="Microsoft.Extensions.Identity.Stores" Version="8.0.10" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
    <PackageVersion Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.6" />
    <PackageVersion Include="NSwag.AspNetCore" Version="14.2.0" />
    <PackageVersion Include="ObjectCloner.Extensions" Version="2.0.1" />
    <PackageVersion Include="Scalar.AspNetCore" Version="1.2.45" />
    <PackageVersion Include="Serilog" Version="4.2.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageVersion Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="7.1.2" />
    <PackageVersion Include="Ulid" Version="1.3.4" />
    <PackageVersion Include="BootstrapBlazor" Version="9.7.4" />
    <PackageVersion Include="BootstrapBlazor.FontAwesome" Version="9.0.2" />
    <PackageVersion Include="BootstrapBlazor.Html2Pdf" Version="9.0.2" />
    <PackageVersion Include="BootstrapBlazor.TableExport" Version="9.2.6" />
    <PackageVersion Include="FluentValidation" Version="12.0.0" />
    <!-- Test packages -->
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="xunit" Version="2.6.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.5.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageVersion>
    <PackageVersion Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageVersion>
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="Moq" Version="4.20.69" />
    <PackageVersion Include="AutoFixture" Version="4.18.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.10" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.10" />
    <PackageVersion Include="Testcontainers" Version="3.9.0" />
  </ItemGroup>
</Project>