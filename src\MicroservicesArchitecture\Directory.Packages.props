<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <ItemGroup>
    <!-- Microsoft Extensions -->
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="8.0.0" />

    <!-- ASP.NET Core -->
    <PackageVersion Include="Microsoft.AspNetCore.App" Version="8.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authorization" Version="8.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />

    <!-- Entity Framework Core -->
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />

    <!-- MediatR - Replaced with Custom Mediator -->
    <!-- <PackageVersion Include="MediatR" Version="12.2.0" /> -->
    <!-- <PackageVersion Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" /> -->

    <!-- AutoMapper -->
    <PackageVersion Include="AutoMapper" Version="12.0.1" />
    <PackageVersion Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />

    <!-- FluentValidation -->
    <PackageVersion Include="FluentValidation" Version="11.8.1" />
    <PackageVersion Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.8.1" />

    <!-- Swagger/OpenAPI -->
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />

    <!-- gRPC -->
    <PackageVersion Include="Grpc.AspNetCore" Version="2.59.0" />
    <PackageVersion Include="Grpc.Tools" Version="2.59.0" />
    <PackageVersion Include="Google.Protobuf" Version="3.25.1" />

    <!-- Message Brokers -->
    <PackageVersion Include="RabbitMQ.Client" Version="6.8.1" />
    <PackageVersion Include="MassTransit" Version="8.1.3" />
    <PackageVersion Include="MassTransit.RabbitMQ" Version="8.1.3" />

    <!-- Caching -->
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.0" />
    <PackageVersion Include="StackExchange.Redis" Version="2.7.10" />

    <!-- Health Checks -->
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI" Version="7.0.2" />
    <PackageVersion Include="AspNetCore.HealthChecks.SqlServer" Version="7.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.Redis" Version="7.0.1" />
    <PackageVersion Include="AspNetCore.HealthChecks.RabbitMQ" Version="7.0.0" />

    <!-- Observability -->
    <PackageVersion Include="OpenTelemetry" Version="1.7.0" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.7.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.7.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.7.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.SqlClient" Version="1.7.0-beta.1" />
    <PackageVersion Include="OpenTelemetry.Exporter.Jaeger" Version="1.5.1" />
    <PackageVersion Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.7.0-beta.1" />

    <!-- Serilog -->
    <PackageVersion Include="Serilog" Version="3.1.1" />
    <PackageVersion Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageVersion Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageVersion Include="Serilog.Sinks.Elasticsearch" Version="9.0.3" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageVersion Include="Serilog.Enrichers.Environment" Version="2.3.0" />
    <PackageVersion Include="Serilog.Enrichers.Process" Version="2.0.2" />
    <PackageVersion Include="Serilog.Enrichers.Thread" Version="3.1.0" />

    <!-- Testing -->
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="xunit" Version="2.6.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.5.3" />
    <PackageVersion Include="coverlet.collector" Version="6.0.0" />
    <PackageVersion Include="Moq" Version="4.20.69" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.0" />
    <PackageVersion Include="Testcontainers" Version="3.6.0" />
    <PackageVersion Include="Testcontainers.SqlServer" Version="3.6.0" />
    <PackageVersion Include="Testcontainers.Redis" Version="3.6.0" />
    <PackageVersion Include="Testcontainers.RabbitMq" Version="3.6.0" />

    <!-- Security -->
    <PackageVersion Include="Microsoft.IdentityModel.Tokens" Version="7.0.3" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="7.0.3" />

    <!-- Utilities -->
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="System.Text.Json" Version="8.0.0" />
    <PackageVersion Include="Polly" Version="8.2.0" />
    <PackageVersion Include="Polly.Extensions.Http" Version="3.0.0" />
  </ItemGroup>

</Project>
