﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35312.102
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{BC44EA12-EF7F-499C-9FCF-F55718C83674}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Application", "Application", "{FDD17445-D179-45D1-AC7F-F055EF7D4C84}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductManager.Application", "src\Application\ProductManager.Application\ProductManager.Application.csproj", "{95EABED1-39BB-493C-BA55-652933E5E4DA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductManager.Domain", "src\Application\ProductManager.Domain\ProductManager.Domain.csproj", "{4EABD4C9-7895-49C9-957E-23A02495F6D7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{4C6EBB09-2BB6-43B5-B9FF-4D0339DA22AA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductManager.Infrastructure", "src\Infrastructure\ProductManager.Infrastructure\ProductManager.Infrastructure.csproj", "{08EB2E30-3E2B-4377-AA1A-1287B53CBC99}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductManager.Persistence", "src\Infrastructure\ProductManager.Persistence\ProductManager.Persistence.csproj", "{0DBA7E40-9C37-4037-9D0F-0E59C6B715DB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentation", "Presentation", "{7DB118A8-B133-4E5C-8BA2-0A434E7C4D4C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductManager.Api", "src\Presentation\APIs\ProductManager.Api\ProductManager.Api.csproj", "{0A62CED1-E975-4973-B210-49F0AF7C47E4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{FB07BEB4-D582-478C-98D0-63186F1BFCAC}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		Clean.bat = Clean.bat
		Clean.sh = Clean.sh
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Shared", "Shared", "{1A954718-90ED-4AB9-A5BB-BD7BB2173241}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProductManager.Shared", "src\CrossCuttingConcerns\ProductManager.Shared\ProductManager.Shared.csproj", "{87C1E557-E074-49A3-AF04-EEFCAC8F97D2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProductManager.Constants", "src\CrossCuttingConcerns\ProductManager.Constants\ProductManager.Constants.csproj", "{E5EF3095-855D-419C-BD42-A3744CABCA2C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "APIs", "APIs", "{941A3550-52B2-48DB-8020-C18D4C6F764D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{8B83C0C5-E4D2-4C5F-9F8A-2D5E3B7A9C8D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductManager.UnitTests", "tests\UnitTests\ProductManager.UnitTests.csproj", "{3F6A8B2C-1D9E-4A7F-8C5B-9E2F5A8D7C6B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductManager.IntegrationTests", "tests\IntegrationTests\ProductManager.IntegrationTests.csproj", "{7E4C9B5A-2F8D-4B6C-9A3E-1F5C8D7B4A9E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{95EABED1-39BB-493C-BA55-652933E5E4DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{95EABED1-39BB-493C-BA55-652933E5E4DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{95EABED1-39BB-493C-BA55-652933E5E4DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{95EABED1-39BB-493C-BA55-652933E5E4DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{4EABD4C9-7895-49C9-957E-23A02495F6D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4EABD4C9-7895-49C9-957E-23A02495F6D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4EABD4C9-7895-49C9-957E-23A02495F6D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4EABD4C9-7895-49C9-957E-23A02495F6D7}.Release|Any CPU.Build.0 = Release|Any CPU
		{08EB2E30-3E2B-4377-AA1A-1287B53CBC99}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{08EB2E30-3E2B-4377-AA1A-1287B53CBC99}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{08EB2E30-3E2B-4377-AA1A-1287B53CBC99}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{08EB2E30-3E2B-4377-AA1A-1287B53CBC99}.Release|Any CPU.Build.0 = Release|Any CPU
		{0DBA7E40-9C37-4037-9D0F-0E59C6B715DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0DBA7E40-9C37-4037-9D0F-0E59C6B715DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0DBA7E40-9C37-4037-9D0F-0E59C6B715DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0DBA7E40-9C37-4037-9D0F-0E59C6B715DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{0A62CED1-E975-4973-B210-49F0AF7C47E4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0A62CED1-E975-4973-B210-49F0AF7C47E4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0A62CED1-E975-4973-B210-49F0AF7C47E4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0A62CED1-E975-4973-B210-49F0AF7C47E4}.Release|Any CPU.Build.0 = Release|Any CPU
		{87C1E557-E074-49A3-AF04-EEFCAC8F97D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{87C1E557-E074-49A3-AF04-EEFCAC8F97D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{87C1E557-E074-49A3-AF04-EEFCAC8F97D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{87C1E557-E074-49A3-AF04-EEFCAC8F97D2}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5EF3095-855D-419C-BD42-A3744CABCA2C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5EF3095-855D-419C-BD42-A3744CABCA2C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5EF3095-855D-419C-BD42-A3744CABCA2C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5EF3095-855D-419C-BD42-A3744CABCA2C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2BF3280-4983-4F67-9289-BBF6AE798A61}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2BF3280-4983-4F67-9289-BBF6AE798A61}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2BF3280-4983-4F67-9289-BBF6AE798A61}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2BF3280-4983-4F67-9289-BBF6AE798A61}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E4C9B5A-2F8D-4B6C-9A3E-1F5C8D7B4A9E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E4C9B5A-2F8D-4B6C-9A3E-1F5C8D7B4A9E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E4C9B5A-2F8D-4B6C-9A3E-1F5C8D7B4A9E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E4C9B5A-2F8D-4B6C-9A3E-1F5C8D7B4A9E}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F6A8B2C-1D9E-4A7F-8C5B-9E2F5A8D7C6B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F6A8B2C-1D9E-4A7F-8C5B-9E2F5A8D7C6B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F6A8B2C-1D9E-4A7F-8C5B-9E2F5A8D7C6B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F6A8B2C-1D9E-4A7F-8C5B-9E2F5A8D7C6B}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{FDD17445-D179-45D1-AC7F-F055EF7D4C84} = {BC44EA12-EF7F-499C-9FCF-F55718C83674}
		{95EABED1-39BB-493C-BA55-652933E5E4DA} = {FDD17445-D179-45D1-AC7F-F055EF7D4C84}
		{4EABD4C9-7895-49C9-957E-23A02495F6D7} = {FDD17445-D179-45D1-AC7F-F055EF7D4C84}
		{4C6EBB09-2BB6-43B5-B9FF-4D0339DA22AA} = {BC44EA12-EF7F-499C-9FCF-F55718C83674}
		{08EB2E30-3E2B-4377-AA1A-1287B53CBC99} = {4C6EBB09-2BB6-43B5-B9FF-4D0339DA22AA}
		{0DBA7E40-9C37-4037-9D0F-0E59C6B715DB} = {4C6EBB09-2BB6-43B5-B9FF-4D0339DA22AA}
		{7DB118A8-B133-4E5C-8BA2-0A434E7C4D4C} = {BC44EA12-EF7F-499C-9FCF-F55718C83674}
		{1A954718-90ED-4AB9-A5BB-BD7BB2173241} = {BC44EA12-EF7F-499C-9FCF-F55718C83674}
		{87C1E557-E074-49A3-AF04-EEFCAC8F97D2} = {1A954718-90ED-4AB9-A5BB-BD7BB2173241}
		{E5EF3095-855D-419C-BD42-A3744CABCA2C} = {1A954718-90ED-4AB9-A5BB-BD7BB2173241}
		{3AE4868D-3514-4537-929D-3071E530C9F0} = {7DB118A8-B133-4E5C-8BA2-0A434E7C4D4C}
		{A2BF3280-4983-4F67-9289-BBF6AE798A61} = {3AE4868D-3514-4537-929D-3071E530C9F0}
		{941A3550-52B2-48DB-8020-C18D4C6F764D} = {7DB118A8-B133-4E5C-8BA2-0A434E7C4D4C}
		{0A62CED1-E975-4973-B210-49F0AF7C47E4} = {941A3550-52B2-48DB-8020-C18D4C6F764D}
		{7E4C9B5A-2F8D-4B6C-9A3E-1F5C8D7B4A9E} = {8B83C0C5-E4D2-4C5F-9F8A-2D5E3B7A9C8D}
		{3F6A8B2C-1D9E-4A7F-8C5B-9E2F5A8D7C6B} = {8B83C0C5-E4D2-4C5F-9F8A-2D5E3B7A9C8D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9C969E28-A5A7-4916-9E82-C3ADE21AED92}
	EndGlobalSection
EndGlobal
