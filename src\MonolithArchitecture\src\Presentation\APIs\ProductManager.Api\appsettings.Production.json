{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Error", "ProductManager": "Information"}}, "App": {"Name": "ProductManager Production", "Version": "1.0.0", "Environment": "Production", "Database": {"DefaultConnection": "#{ProductManager.Database.ConnectionString}#", "IdentityConnection": "#{ProductManager.Identity.ConnectionString}#", "CommandTimeout": 60, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "Security": {"JwtSecret": "#{ProductManager.Security.JwtSecret}#", "JwtIssuer": "#{ProductManager.Security.JwtIssuer}#", "JwtAudience": "#{ProductManager.Security.JwtAudience}#", "JwtExpirationMinutes": 30, "RefreshTokenExpirationDays": 1, "RequireHttps": true, "AllowedOrigins": ["#{ProductManager.Security.AllowedOrigins}#"], "BearerTokenExpiration": "00:30:00", "RefreshTokenExpiration": "1.00:00:00", "EmailTokenLifetime": "24:00:00", "PhoneNumberTokenLifetime": "00:05:00", "ResetPasswordTokenLifetime": "01:00:00", "TwoFactorTokenLifetime": "00:05:00", "OtpTokenLifetime": "00:05:00", "RevokeUserSessionsDelay": "00:00:30", "Password": {"RequireDigit": true, "RequiredLength": 12, "RequireNonAlphanumeric": true, "RequireUppercase": true, "RequireLowercase": true}, "SignIn": {"RequireConfirmedAccount": true}}, "Logging": {"LogLevel": "Warning", "EnableFileLogging": true, "LogPath": "/app/logs", "MaxFileSizeMB": 50, "MaxFiles": 20, "EnableStructuredLogging": true}, "Cache": {"EnableDistributedCache": true, "ConnectionString": "#{ProductManager.Cache.ConnectionString}#", "DefaultExpirationMinutes": 60}}, "AllowedHosts": "#{ProductManager.Security.AllowedHosts}#", "RequestSigning": {"RequireSignedRequests": true, "SecretKey": "#{ProductManager.Security.RequestSigningKey}#", "MaxTimestampAge": "00:05:00"}, "IpWhitelist": {"EnableWhitelist": true, "AllowedIps": ["#{ProductManager.Security.AllowedIPs}#"]}, "RateLimit": {"EnableRateLimit": true, "MaxRequests": 100, "Window": "00:01:00"}, "Cors": {"PolicyName": "Production", "AllowAnyOrigin": false, "AllowAnyMethod": false, "AllowAnyHeader": false, "AllowCredentials": true}, "Swagger": {"Enabled": false, "Title": "ProductManager Production API", "Version": "v1.0", "EnableTryItOut": false, "DisplayRequestDuration": false}}