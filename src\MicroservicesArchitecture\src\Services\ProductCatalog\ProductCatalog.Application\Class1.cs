﻿using ProductCatalog.Domain.Entities;

namespace ProductCatalog.Application.Common.Interfaces;

/// <summary>
/// Product repository interface
/// </summary>
public interface IProductRepository
{
  Task<Product?> GetByIdAsync(string id, CancellationToken cancellationToken = default);
  Task<IEnumerable<Product>> GetAllAsync(CancellationToken cancellationToken = default);
  Task<IEnumerable<Product>> GetPagedAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default);
  Task<IEnumerable<Product>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
  Task<IEnumerable<Product>> GetByCategoryIdAsync(string categoryId, CancellationToken cancellationToken = default);
  Task<IEnumerable<Product>> GetBySupplierIdAsync(string supplierId, CancellationToken cancellationToken = default);
  Task<Product> AddAsync(Product product, CancellationToken cancellationToken = default);
  Task UpdateAsync(Product product, CancellationToken cancellationToken = default);
  Task DeleteAsync(string id, CancellationToken cancellationToken = default);
  Task<bool> ExistsAsync(string id, CancellationToken cancellationToken = default);
}

/// <summary>
/// Category repository interface
/// </summary>
public interface ICategoryRepository
{
  Task<Category?> GetByIdAsync(string id, CancellationToken cancellationToken = default);
  Task<IEnumerable<Category>> GetAllAsync(CancellationToken cancellationToken = default);
  Task<IEnumerable<Category>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
  Task<Category> AddAsync(Category category, CancellationToken cancellationToken = default);
  Task UpdateAsync(Category category, CancellationToken cancellationToken = default);
  Task DeleteAsync(string id, CancellationToken cancellationToken = default);
  Task<bool> ExistsAsync(string id, CancellationToken cancellationToken = default);
}

/// <summary>
/// Supplier repository interface
/// </summary>
public interface ISupplierRepository
{
  Task<Supplier?> GetByIdAsync(string id, CancellationToken cancellationToken = default);
  Task<IEnumerable<Supplier>> GetAllAsync(CancellationToken cancellationToken = default);
  Task<IEnumerable<Supplier>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
  Task<Supplier> AddAsync(Supplier supplier, CancellationToken cancellationToken = default);
  Task UpdateAsync(Supplier supplier, CancellationToken cancellationToken = default);
  Task DeleteAsync(string id, CancellationToken cancellationToken = default);
  Task<bool> ExistsAsync(string id, CancellationToken cancellationToken = default);
}

/// <summary>
/// Unit of work interface
/// </summary>
public interface IUnitOfWork
{
  IProductRepository Products { get; }
  ICategoryRepository Categories { get; }
  ISupplierRepository Suppliers { get; }
  Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
  Task BeginTransactionAsync(CancellationToken cancellationToken = default);
  Task CommitTransactionAsync(CancellationToken cancellationToken = default);
  Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
