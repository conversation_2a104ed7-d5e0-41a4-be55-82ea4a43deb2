{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Services": {"ProductCatalogService": {"BaseUrl": "https://localhost:7001"}, "CustomerManagementService": {"BaseUrl": "https://localhost:7002"}, "OrderManagementService": {"BaseUrl": "https://localhost:7003"}, "InventoryManagementService": {"BaseUrl": "https://localhost:7004"}, "ShippingLogisticsService": {"BaseUrl": "https://localhost:7005"}, "IdentityAccessService": {"BaseUrl": "https://localhost:7006"}}, "Jwt": {"Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "MicroservicesArchitecture", "Audience": "MicroservicesArchitecture"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/apigateway-.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}