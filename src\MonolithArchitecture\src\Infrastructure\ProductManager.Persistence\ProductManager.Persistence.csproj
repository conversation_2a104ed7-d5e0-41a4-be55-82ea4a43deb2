﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer"/>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Application\ProductManager.Domain\ProductManager.Domain.csproj"/>
    <ProjectReference Include="..\..\CrossCuttingConcerns\ProductManager.Constants\ProductManager.Constants.csproj"/>
    <ProjectReference Include="..\ProductManager.Infrastructure\ProductManager.Infrastructure.csproj"/>
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.json">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="appsettings.Development.json">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
