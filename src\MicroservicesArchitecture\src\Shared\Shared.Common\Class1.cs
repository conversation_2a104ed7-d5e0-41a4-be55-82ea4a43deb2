﻿namespace Shared.Common.Extensions;

/// <summary>
/// String extension methods
/// </summary>
public static class StringExtensions
{
  /// <summary>
  /// Check if string is null or empty
  /// </summary>
  public static bool IsNullOrEmpty(this string? value)
  {
    return string.IsNullOrEmpty(value);
  }

  /// <summary>
  /// Check if string is null or whitespace
  /// </summary>
  public static bool IsNullOrWhiteSpace(this string? value)
  {
    return string.IsNullOrWhiteSpace(value);
  }

  /// <summary>
  /// Convert string to title case
  /// </summary>
  public static string ToTitleCase(this string value)
  {
    if (value.IsNullOrEmpty())
      return value;

    return System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(value.ToLower());
  }

  /// <summary>
  /// Truncate string to specified length
  /// </summary>
  public static string Truncate(this string value, int maxLength)
  {
    if (value.IsNullOrEmpty() || value.Length <= maxLength)
      return value;

    return value[..maxLength];
  }
}
