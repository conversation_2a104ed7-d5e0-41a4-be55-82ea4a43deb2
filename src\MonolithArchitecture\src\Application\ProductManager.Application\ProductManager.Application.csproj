﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>ProductManager.UnitTests</_Parameter1>
    </AssemblyAttribute>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>ProductManager.IntegrationTests</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Mapster"/>
    <PackageReference Include="MediatR"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation"/>
    <ProjectReference Include="..\..\CrossCuttingConcerns\ProductManager.Constants\ProductManager.Constants.csproj"/>
    <ProjectReference Include="..\..\CrossCuttingConcerns\ProductManager.Shared\ProductManager.Shared.csproj"/>
    <ProjectReference Include="..\..\Infrastructure\ProductManager.Persistence\ProductManager.Persistence.csproj"/>
    <ProjectReference Include="..\ProductManager.Domain\ProductManager.Domain.csproj"/>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Feature\AuditLogEntries\DTOs\"/>
  </ItemGroup>

</Project>
