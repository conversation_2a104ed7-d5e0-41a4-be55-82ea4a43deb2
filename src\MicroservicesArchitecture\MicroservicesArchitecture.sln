
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{5854FF58-B8C5-05C6-5BF2-ACE42193F22B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Shared", "Shared", "{4F52FD11-658E-A102-6CD3-7D7C16FFA15B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{DFEE68D8-A7D0-1A41-506C-9B74AA9A7B7B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Gateway", "Gateway", "{E7BDDBC6-9FD1-D1D7-ACD8-2C4F8E3D2461}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ProductCatalog", "ProductCatalog", "{11111111-1111-1111-1111-111111111111}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductCatalog.Domain", "src\Services\ProductCatalog\ProductCatalog.Domain\ProductCatalog.Domain.csproj", "{22222222-2222-2222-2222-222222222222}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductCatalog.Application", "src\Services\ProductCatalog\ProductCatalog.Application\ProductCatalog.Application.csproj", "{33333333-3333-3333-3333-333333333333}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductCatalog.Infrastructure", "src\Services\ProductCatalog\ProductCatalog.Infrastructure\ProductCatalog.Infrastructure.csproj", "{44444444-4444-4444-4444-444444444444}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductCatalog.API", "src\Services\ProductCatalog\ProductCatalog.API\ProductCatalog.API.csproj", "{55555555-5555-5555-5555-555555555555}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Shared.Common", "src\Shared\Shared.Common\Shared.Common.csproj", "{66666666-6666-6666-6666-666666666666}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Shared.Contracts", "src\Shared\Shared.Contracts\Shared.Contracts.csproj", "{77777777-7777-7777-7777-777777777777}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Shared.Events", "src\Shared\Shared.Events\Shared.Events.csproj", "{88888888-8888-8888-8888-888888888888}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ApiGateway", "src\Gateway\ApiGateway\ApiGateway.csproj", "{C3667A83-2798-448F-861C-558525832F4D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ProductCatalog", "ProductCatalog", "{0CEFB4F7-3B1E-4F95-9B22-F4B6E9E98689}"
EndProject


Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{22222222-2222-2222-2222-222222222222}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{22222222-2222-2222-2222-222222222222}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{22222222-2222-2222-2222-222222222222}.Debug|x64.ActiveCfg = Debug|Any CPU
		{22222222-2222-2222-2222-222222222222}.Debug|x64.Build.0 = Debug|Any CPU
		{22222222-2222-2222-2222-222222222222}.Debug|x86.ActiveCfg = Debug|Any CPU
		{22222222-2222-2222-2222-222222222222}.Debug|x86.Build.0 = Debug|Any CPU
		{22222222-2222-2222-2222-222222222222}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{22222222-2222-2222-2222-222222222222}.Release|Any CPU.Build.0 = Release|Any CPU
		{22222222-2222-2222-2222-222222222222}.Release|x64.ActiveCfg = Release|Any CPU
		{22222222-2222-2222-2222-222222222222}.Release|x64.Build.0 = Release|Any CPU
		{22222222-2222-2222-2222-222222222222}.Release|x86.ActiveCfg = Release|Any CPU
		{22222222-2222-2222-2222-222222222222}.Release|x86.Build.0 = Release|Any CPU
		{33333333-3333-3333-3333-333333333333}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33333333-3333-3333-3333-333333333333}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{33333333-3333-3333-3333-333333333333}.Debug|x64.ActiveCfg = Debug|Any CPU
		{33333333-3333-3333-3333-333333333333}.Debug|x64.Build.0 = Debug|Any CPU
		{33333333-3333-3333-3333-333333333333}.Debug|x86.ActiveCfg = Debug|Any CPU
		{33333333-3333-3333-3333-333333333333}.Debug|x86.Build.0 = Debug|Any CPU
		{33333333-3333-3333-3333-333333333333}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33333333-3333-3333-3333-333333333333}.Release|Any CPU.Build.0 = Release|Any CPU
		{33333333-3333-3333-3333-333333333333}.Release|x64.ActiveCfg = Release|Any CPU
		{33333333-3333-3333-3333-333333333333}.Release|x64.Build.0 = Release|Any CPU
		{33333333-3333-3333-3333-333333333333}.Release|x86.ActiveCfg = Release|Any CPU
		{33333333-3333-3333-3333-333333333333}.Release|x86.Build.0 = Release|Any CPU
		{44444444-4444-4444-4444-444444444444}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44444444-4444-4444-4444-444444444444}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44444444-4444-4444-4444-444444444444}.Debug|x64.ActiveCfg = Debug|Any CPU
		{44444444-4444-4444-4444-444444444444}.Debug|x64.Build.0 = Debug|Any CPU
		{44444444-4444-4444-4444-444444444444}.Debug|x86.ActiveCfg = Debug|Any CPU
		{44444444-4444-4444-4444-444444444444}.Debug|x86.Build.0 = Debug|Any CPU
		{44444444-4444-4444-4444-444444444444}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44444444-4444-4444-4444-444444444444}.Release|Any CPU.Build.0 = Release|Any CPU
		{44444444-4444-4444-4444-444444444444}.Release|x64.ActiveCfg = Release|Any CPU
		{44444444-4444-4444-4444-444444444444}.Release|x64.Build.0 = Release|Any CPU
		{44444444-4444-4444-4444-444444444444}.Release|x86.ActiveCfg = Release|Any CPU
		{44444444-4444-4444-4444-444444444444}.Release|x86.Build.0 = Release|Any CPU
		{55555555-5555-5555-5555-555555555555}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{55555555-5555-5555-5555-555555555555}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{55555555-5555-5555-5555-555555555555}.Debug|x64.ActiveCfg = Debug|Any CPU
		{55555555-5555-5555-5555-555555555555}.Debug|x64.Build.0 = Debug|Any CPU
		{55555555-5555-5555-5555-555555555555}.Debug|x86.ActiveCfg = Debug|Any CPU
		{55555555-5555-5555-5555-555555555555}.Debug|x86.Build.0 = Debug|Any CPU
		{55555555-5555-5555-5555-555555555555}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{55555555-5555-5555-5555-555555555555}.Release|Any CPU.Build.0 = Release|Any CPU
		{55555555-5555-5555-5555-555555555555}.Release|x64.ActiveCfg = Release|Any CPU
		{55555555-5555-5555-5555-555555555555}.Release|x64.Build.0 = Release|Any CPU
		{55555555-5555-5555-5555-555555555555}.Release|x86.ActiveCfg = Release|Any CPU
		{55555555-5555-5555-5555-555555555555}.Release|x86.Build.0 = Release|Any CPU
		{66666666-6666-6666-6666-666666666666}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66666666-6666-6666-6666-666666666666}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66666666-6666-6666-6666-666666666666}.Debug|x64.ActiveCfg = Debug|Any CPU
		{66666666-6666-6666-6666-666666666666}.Debug|x64.Build.0 = Debug|Any CPU
		{66666666-6666-6666-6666-666666666666}.Debug|x86.ActiveCfg = Debug|Any CPU
		{66666666-6666-6666-6666-666666666666}.Debug|x86.Build.0 = Debug|Any CPU
		{66666666-6666-6666-6666-666666666666}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66666666-6666-6666-6666-666666666666}.Release|Any CPU.Build.0 = Release|Any CPU
		{66666666-6666-6666-6666-666666666666}.Release|x64.ActiveCfg = Release|Any CPU
		{66666666-6666-6666-6666-666666666666}.Release|x64.Build.0 = Release|Any CPU
		{66666666-6666-6666-6666-666666666666}.Release|x86.ActiveCfg = Release|Any CPU
		{66666666-6666-6666-6666-666666666666}.Release|x86.Build.0 = Release|Any CPU
		{77777777-7777-7777-7777-777777777777}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{77777777-7777-7777-7777-777777777777}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{77777777-7777-7777-7777-777777777777}.Debug|x64.ActiveCfg = Debug|Any CPU
		{77777777-7777-7777-7777-777777777777}.Debug|x64.Build.0 = Debug|Any CPU
		{77777777-7777-7777-7777-777777777777}.Debug|x86.ActiveCfg = Debug|Any CPU
		{77777777-7777-7777-7777-777777777777}.Debug|x86.Build.0 = Debug|Any CPU
		{77777777-7777-7777-7777-777777777777}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{77777777-7777-7777-7777-777777777777}.Release|Any CPU.Build.0 = Release|Any CPU
		{77777777-7777-7777-7777-777777777777}.Release|x64.ActiveCfg = Release|Any CPU
		{77777777-7777-7777-7777-777777777777}.Release|x64.Build.0 = Release|Any CPU
		{77777777-7777-7777-7777-777777777777}.Release|x86.ActiveCfg = Release|Any CPU
		{77777777-7777-7777-7777-777777777777}.Release|x86.Build.0 = Release|Any CPU
		{88888888-8888-8888-8888-888888888888}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{88888888-8888-8888-8888-888888888888}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{88888888-8888-8888-8888-888888888888}.Debug|x64.ActiveCfg = Debug|Any CPU
		{88888888-8888-8888-8888-888888888888}.Debug|x64.Build.0 = Debug|Any CPU
		{88888888-8888-8888-8888-888888888888}.Debug|x86.ActiveCfg = Debug|Any CPU
		{88888888-8888-8888-8888-888888888888}.Debug|x86.Build.0 = Debug|Any CPU
		{88888888-8888-8888-8888-888888888888}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{88888888-8888-8888-8888-888888888888}.Release|Any CPU.Build.0 = Release|Any CPU
		{88888888-8888-8888-8888-888888888888}.Release|x64.ActiveCfg = Release|Any CPU
		{88888888-8888-8888-8888-888888888888}.Release|x64.Build.0 = Release|Any CPU
		{88888888-8888-8888-8888-888888888888}.Release|x86.ActiveCfg = Release|Any CPU
		{88888888-8888-8888-8888-888888888888}.Release|x86.Build.0 = Release|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Debug|x64.Build.0 = Debug|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Debug|x86.Build.0 = Debug|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Release|x64.ActiveCfg = Release|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Release|x64.Build.0 = Release|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Release|x86.ActiveCfg = Release|Any CPU
		{C3667A83-2798-448F-861C-558525832F4D}.Release|x86.Build.0 = Release|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Debug|x64.Build.0 = Debug|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Debug|x86.Build.0 = Debug|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Release|x64.ActiveCfg = Release|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Release|x64.Build.0 = Release|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Release|x86.ActiveCfg = Release|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Release|x86.Build.0 = Release|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Debug|x64.Build.0 = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Debug|x86.Build.0 = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Release|Any CPU.Build.0 = Release|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Release|x64.ActiveCfg = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Release|x64.Build.0 = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Release|x86.ActiveCfg = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Release|x86.Build.0 = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Debug|x64.Build.0 = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Debug|x86.Build.0 = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Release|Any CPU.Build.0 = Release|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Release|x64.ActiveCfg = Release|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Release|x64.Build.0 = Release|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Release|x86.ActiveCfg = Release|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Release|x86.Build.0 = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|x64.Build.0 = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|x86.Build.0 = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|Any CPU.Build.0 = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|x64.ActiveCfg = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|x64.Build.0 = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|x86.ActiveCfg = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E7BDDBC6-9FD1-D1D7-ACD8-2C4F8E3D2461} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{C3667A83-2798-448F-861C-558525832F4D} = {E7BDDBC6-9FD1-D1D7-ACD8-2C4F8E3D2461}
		{DFEE68D8-A7D0-1A41-506C-9B74AA9A7B7B} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{11111111-1111-1111-1111-111111111111} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{5854FF58-B8C5-05C6-5BF2-ACE42193F22B} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{4F52FD11-658E-A102-6CD3-7D7C16FFA15B} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{0CEFB4F7-3B1E-4F95-9B22-F4B6E9E98689} = {5854FF58-B8C5-05C6-5BF2-ACE42193F22B}
		{55555555-5555-5555-5555-555555555555} = {0CEFB4F7-3B1E-4F95-9B22-F4B6E9E98689}
		{33333333-3333-3333-3333-333333333333} = {0CEFB4F7-3B1E-4F95-9B22-F4B6E9E98689}
		{22222222-2222-2222-2222-222222222222} = {0CEFB4F7-3B1E-4F95-9B22-F4B6E9E98689}
		{44444444-4444-4444-4444-444444444444} = {0CEFB4F7-3B1E-4F95-9B22-F4B6E9E98689}
		{66666666-6666-6666-6666-666666666666} = {4F52FD11-658E-A102-6CD3-7D7C16FFA15B}
		{77777777-7777-7777-7777-777777777777} = {4F52FD11-658E-A102-6CD3-7D7C16FFA15B}
		{88888888-8888-8888-8888-888888888888} = {4F52FD11-658E-A102-6CD3-7D7C16FFA15B}
		{*************-9999-9999-************} = {5854FF58-B8C5-05C6-5BF2-ACE42193F22B}
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA} = {*************-9999-9999-************}
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB} = {*************-9999-9999-************}
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC} = {*************-9999-9999-************}
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD} = {*************-9999-9999-************}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
